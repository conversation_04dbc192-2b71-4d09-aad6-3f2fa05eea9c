<!--- main --->
<div class="row gf-list">
    <div class="col-md-9">
        <ul class="list-group">

            <li class="list-group-item">
                <div class="float-left">
                    <a class="btn btn-link {{if eq $.Query.cate 1}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm" href="/?cate=1&sort={{$.Query.sort}}">随聊</a>
                    <a class="btn btn-link {{if eq $.Query.cate 6}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm" href="/?cate=6&sort={{$.Query.sort}}">翻译</a>
                    <a class="btn btn-link {{if eq $.Query.cate 7}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm" href="/?cate=7&sort={{$.Query.sort}}">分享</a>
                    <a class="btn btn-link {{if eq $.Query.cate 12}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm" href="/?cate=12&sort={{$.Query.sort}}">招聘</a>

                </div>

                <div class="float-right">
                    <a class="btn btn-link {{if eq .Query.sort 0}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
                       href="/?cate={{.Query.cate}}&sort=0">最新</a>
                    <a class="btn btn-link {{if eq .Query.sort 1}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
                       href="/?cate={{.Query.cate}}&sort=1">活跃</a>
                    <a class="btn btn-link {{if eq .Query.sort 2}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
                       href="/?cate={{.Query.cate}}&sort=2">热度</a>
                </div>
            </li>

            {{range $index, $item := .Data.List}}
                <li class="list-group-item gf-list-item">
                    <div class="gf-list-item-img">
                        <img src="{{$item.User.Avatar}}" style="height: 36px;width:36px;">
                    </div>
                    <div class="gf-list-item-text">
                        <div class="float-left">
                            <span class="badge  {{if eq $item.Category.ContentType `ask`}}badge-primary{{else}}badge-success{{end}}">{{$item.Category.Name}}</span>
                            <span class="gf-list-item-title">
                            <a href="/{{$item.Category.ContentType}}/{{$item.Content.Id}}"
                               style="color: #222;" target="_blank">{{$item.Content.Title}}
                                        {{if  $item.Content.CreatedAt | $.BuildIn.IsNew }}<span class="gf-content-new">new</span>{{end}}</a>
                        </span>
                        </div>
                        <div class="float-right">
                                <span style="font-size: 12px;color: #ccc;text-align: right;">
                                    <span class="icon iconfont">&#xe660;</span> {{$item.Content.ViewCount}}
                                    &nbsp;&nbsp;|&nbsp;&nbsp;
                                    <span class="icon iconfont">&#xe6ab;</span> {{$item.Content.ReplyCount}}
                                    &nbsp;&nbsp;|&nbsp;&nbsp;
                                </span>
                            <span style="font-size: 12px;color: #ccc;">{{$item.Content.CreatedAt | $.BuildIn.FormatTime}}</span>

                        </div>
                    </div>
                </li>
            {{end}}

            {{if gt .Data.Total .Data.Size}}
                <li class="list-group-item gf-list-item">
                    <ul class="pagination">
                        {{.BuildIn.Page .Data.Total .Data.Size }}
                    </ul>
                </li>
            {{end}}

        </ul>
    </div>

    <div class="col-md-3">

        <div class="card gf-card" style="background: #fff;padding: 10px;">
            <a class="btn btn-primary" onclick="window.location.href='/content/create?type=article'"
               href="javascript:void(0);">发布文章</a>
        </div>

        {{include "index/index/page_link.html" .}}
    </div>
</div>
