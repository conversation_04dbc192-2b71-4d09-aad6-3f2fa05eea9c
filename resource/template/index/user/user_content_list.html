{{if .ContentType}}
<li class="list-group-item gf-list-item">
    <a class="btn btn-link {{if not $.Query.cate}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
       href="/user/{{$.ContentType}}?sort={{.Query.sort}}">全部</a>
    {{range $index, $item := .BuildIn.CategoryTree .ContentType}}
    <a class="btn btn-link {{if eq $.Query.cate $item.Id}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
       href="/user/{{$.ContentType}}?cate={{$item.Id}}&sort={{$.Query.sort}}">{{$item.Name}}</a>
    {{end}}
    <div class="float-right">
        <a class="btn btn-link {{if eq .Query.sort 0}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
           href="/user/{{$.ContentType}}?cate={{.Query.cate}}&sort=0">最新</a>
        <a class="btn btn-link {{if eq .Query.sort 1}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
           href="/user/{{$.ContentType}}?cate={{.Query.cate}}&sort=1">活跃</a>
        <a class="btn btn-link {{if eq .Query.sort 2}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
           href="/user/{{$.ContentType}}?cate={{.Query.cate}}&sort=2">热度</a>
    </div>
</li>
{{else}}
<li class="list-group-item">
    <div class="float-right">
        <a class="btn btn-link {{if eq .Query.sort 0}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
           href="/user/{{$.ContentType}}?cate={{.Query.cate}}&sort=0">最新</a>
        <a class="btn btn-link {{if eq .Query.sort 1}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
           href="/user/{{$.ContentType}}?cate={{.Query.cate}}&sort=1">活跃</a>
        <a class="btn btn-link {{if eq .Query.sort 2}}text-primary{{else}}text-secondary{{end}} top-node-btn btn-sm"
           href="/user/{{$.ContentType}}?cate={{.Query.cate}}&sort=2">热度</a>
    </div>
</li>
{{end}}

{{if .Data.Content}}
{{range $index, $item := .Data.Content.List}}
<li class="list-group-item gf-list-item">
    <div class="gf-list-item-img">
        <img src="{{$item.User.Avatar}}" style="height: 36px;width:36px;">
    </div>
    <div class="gf-list-item-text">
        <div class="float-left">
            <span class="badge badge-primary">{{$item.Category.Name}}</span>
            <span class="gf-list-item-title">
                            <a href="/{{$item.Category.ContentType}}/{{$item.Content.Id}}"
                               style="color: #222;">{{$item.Content.Title}}</a>
                        </span>
        </div>
        <div class="float-right">
                        <span style="font-size: 12px;color: #ccc;text-align: right;">
                            <span class="icon iconfont">&#xe660;</span> {{$item.Content.ViewCount}}
                            &nbsp;&nbsp;|&nbsp;&nbsp;
                            <span class="icon iconfont">&#xe6ab;</span> {{$item.Content.ReplyCount}}
                            &nbsp;&nbsp;|&nbsp;&nbsp;
                        </span>
            <span style="font-size: 12px;color: #ccc;">{{$item.Content.CreatedAt | $.BuildIn.FormatTime}}</span>

            {{if eq $.Data.User.Id $.Context.User.Id}}
            <span style="font-size: 12px;color: #ccc;text-align: right;">
                                &nbsp;|&nbsp;&nbsp; <a href="/content/update?id={{$item.Content.Id}}"><span class="icon iconfont"
                                                                             style="color: darkred;">&#xe780;</span></a>
                                &nbsp;|&nbsp;&nbsp; <a href="javascript:void(0);"
                                                       onclick="gf.content.delete({{$item.Content.Id}},'/user/{{$.ContentType}}')"><span
                    class="icon iconfont" style="color: black;">&#xe63c;</span></a>
                                </span>
            {{end}}
        </div>
    </div>
</li>
{{end}}

{{if gt .Data.Content.Total .Data.Content.Size}}
<li class="list-group-item gf-list-item">
    <ul class="pagination">
        {{.BuildIn.Page .Data.Content.Total .Data.Content.Size }}
    </ul>
</li>
{{end}}
{{end}}