<div class="row gf-list">
    <div class="col-md-2 offset-md-1">
        {{include "index/user/user_menu.html" .}}
    </div>
    <div class="col-md-8">
        <div class="card gf-person">
            <div class="card-header">
                <span class="iconfont">&#xe61f;</span> 修改密码
            </div>
            <div class="card-body">
                <form id="passwordForm" action="/profile/password" method="post">
                    <div class="form-group row">
                        <label for="oldPassword" class="col-sm-3 col-form-label required text-right">原始密码</label>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" id="oldPassword" name="oldPassword" minlength="6" maxlength="20"
                                   placeholder="请输入原始密码" required>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="newPassword" class="col-sm-3 col-form-label required text-right">新密码</label>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" id="newPassword"  name="newPassword" minlength="6" maxlength="20"
                                   placeholder="请输入新密码" required>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="newPassword2" class="col-sm-3 col-form-label required text-right">再次输入新密码</label>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" id="newPassword2" name="newPassword2" minlength="6" maxlength="20"
                                   placeholder="请再次输入新密码" required>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-3 offset-md-3">
                            <button type="submit" class="btn btn-primary btn-block">修改</button>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript">
    jQuery(function ($) {

        $("#user-passwd").addClass("active")

        $('#passwordForm').validate({
            errorElement: 'div',
            errorClass: 'validation-error-block',
            focusInvalid: true,
            rules: {
                newPassword2: {
                    equalTo: "#newPassword"
                },
            },
            messages: {
                newPassword2: {
                    equalTo: "输入的新密码两次不同"
                },
            },
            submitHandler: function (form) {
                $('button[type=submit]').attr('disabled', 'true');
                // 传输加密
                let oldPassword = $('[name="oldPassword"]').val();
                let newPassword = $('[name="newPassword"]').val();
                $('[name="oldPassword"]').val(hex_md5('{{.Context.User.Passport}}' + oldPassword));
                $('[name="newPassword"]').val(hex_md5('{{.Context.User.Passport}}' + newPassword));

                jQuery(form).ajaxSubmit({
                    dataType: 'json',
                    success: function (r, textStatus) {
                        if (r.code <= 0) {
                            swal({
                                text: "修改成功",
                                icon: "success",
                                button: "确定"
                            });
                        } else {
                            if (r.message != "") {
                                swal({
                                    text: r.message,
                                    icon: "warning",
                                    button: "确定"
                                });
                            }
                        }
                        $('[name="oldPassword"]').val(oldPassword);
                        $('[name="newPassword"]').val(newPassword);
                        $('[name="newPassword2"]').val(newPassword);
                    }
                });
                $('button[type=submit]').removeAttr('disabled');
            },
            errorPlacement: function (error, element) {
                element.addClass("is-invalid")
                error.appendTo(element.parent());
            }
        });
    });
</script>