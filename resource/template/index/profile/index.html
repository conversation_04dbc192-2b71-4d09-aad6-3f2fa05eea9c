<div class="row gf-list">
    <div class="col-md-2 offset-md-1">
        {{include "index/user/user_menu.html" .}}
    </div>
    <div class="col-md-8">
        <div class="card gf-person">
            <div class="card-header">
                <span class="iconfont">&#xe622;</span> 基本资料
            </div>
            <div class="card-body">
                <form id="profileForm" action="/profile" method="post">
                    <div class="form-group row">
                        <label for="passport" class="col-sm-2 col-form-label">账号</label>
                        <div class="col-sm-10">
                            <input type="text" readonly class="form-control-plaintext" id="passport" value="{{.Context.User.Passport}}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="nickname" class="col-sm-2 col-form-label">昵称</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="nickname" minlength="3" maxlength="20" name="nickname" value="{{ .Data.Nickname}}"
                                   placeholder="请输入昵称" required>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="gender0" class="col-sm-2 col-form-label">性别</label>
                        <div class="col-sm-10">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="gender0" {{if eq .Data.Gender 0}}checked{{end}} name="gender" value="0" required >
                                <label class="form-check-label" for="gender0">未知</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="gender1" {{if eq .Data.Gender 1}}checked{{end}} name="gender" value="1">
                                <label class="form-check-label" for="gender1">男</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="gender2" {{if eq .Data.Gender 2}}checked{{end}} name="gender" value="2">
                                <label class="form-check-label" for="gender2">女</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-3 offset-md-2">
                            <button type="submit" class="btn btn-primary btn-block">保存</button>
                        </div>
                    </div>


                    <div class="form-group row">

                    </div>
                </form>
            </div>
        </div>
    </div>

</div>
<script>
    jQuery(function ($) {

        $("#user-profile").addClass("active")

        $('#profileForm').validate({
            errorElement: 'div',
            errorClass: 'validation-error-block',
            focusInvalid: true,
            rules: {
            },
            messages: {
                nickname: {
                    required: "请输入昵称"
                },
            },
            submitHandler: function (form) {
                $('button[type=submit]').attr('disabled', 'true');
                // 传输加密

                jQuery(form).ajaxSubmit({
                    dataType: 'json',
                    success: function (r, textStatus) {
                        if (r.code <= 0) {
                            swal({
                                text: "修改成功",
                                icon: "success",
                                button: "确定"
                            }).then((value) => {
                                window.location.href = "/profile";
                            });
                        } else {
                            if (r.message != "") {
                                swal({
                                    text: r.message,
                                    icon: "warning",
                                    button: "确定"
                                });
                            }
                        }
                    }
                });
                $('button[type=submit]').removeAttr('disabled');
            },
            errorPlacement: function (error, element) {
                element.addClass("is-invalid")
                error.appendTo(element.parent());
            }
        });
    });
</script>
