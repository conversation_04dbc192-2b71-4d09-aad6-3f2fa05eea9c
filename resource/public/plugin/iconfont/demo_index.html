<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2163953" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe780;</span>
                <div class="name">edit</div>
                <div class="code-name">&amp;#xe780;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">预览</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68f;</span>
                <div class="name">踩1</div>
                <div class="code-name">&amp;#xe68f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe690;</span>
                <div class="name">踩2</div>
                <div class="code-name">&amp;#xe690;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe698;</span>
                <div class="name">收藏1</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe699;</span>
                <div class="name">收藏2</div>
                <div class="code-name">&amp;#xe699;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69a;</span>
                <div class="name">心1</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69b;</span>
                <div class="name">心2</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a1;</span>
                <div class="name">赞1</div>
                <div class="code-name">&amp;#xe6a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a2;</span>
                <div class="name">赞2</div>
                <div class="code-name">&amp;#xe6a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a4;</span>
                <div class="name">赞4</div>
                <div class="code-name">&amp;#xe6a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a5;</span>
                <div class="name">赞5</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6aa;</span>
                <div class="name">信息12</div>
                <div class="code-name">&amp;#xe6aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ab;</span>
                <div class="name">信息13</div>
                <div class="code-name">&amp;#xe6ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">留言</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">我我我</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">female</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">male</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xead2;</span>
                <div class="name">genderless</div>
                <div class="code-name">&amp;#xead2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">Search</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">成功</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71b;</span>
                <div class="name">asterisk</div>
                <div class="code-name">&amp;#xe71b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">notice</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b3;</span>
                <div class="name">topic</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">ask</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">message</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">avatar</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">password</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">github</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">lock</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">位置</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">耳机</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">article</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">user</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">list</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68c;</span>
                <div class="name">User Settings</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe84f;</span>
                <div class="name">article</div>
                <div class="code-name">&amp;#xe84f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c9;</span>
                <div class="name">log-in</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">register</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6eb;</span>
                <div class="name">auto</div>
                <div class="code-name">&amp;#xe6eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ef;</span>
                <div class="name">all</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f4;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe6f4;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit"></span>
            <div class="name">
              edit
            </div>
            <div class="code-name">.icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yulan"></span>
            <div class="name">
              预览
            </div>
            <div class="code-name">.icon-yulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cai1"></span>
            <div class="name">
              踩1
            </div>
            <div class="code-name">.icon-cai1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cai2"></span>
            <div class="name">
              踩2
            </div>
            <div class="code-name">.icon-cai2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              收藏1
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang1"></span>
            <div class="name">
              收藏2
            </div>
            <div class="code-name">.icon-shoucang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xin"></span>
            <div class="name">
              心1
            </div>
            <div class="code-name">.icon-xin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xin1"></span>
            <div class="name">
              心2
            </div>
            <div class="code-name">.icon-xin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zan"></span>
            <div class="name">
              赞1
            </div>
            <div class="code-name">.icon-zan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zan1"></span>
            <div class="name">
              赞2
            </div>
            <div class="code-name">.icon-zan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zan2"></span>
            <div class="name">
              赞4
            </div>
            <div class="code-name">.icon-zan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zan3"></span>
            <div class="name">
              赞5
            </div>
            <div class="code-name">.icon-zan3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinxi"></span>
            <div class="name">
              信息12
            </div>
            <div class="code-name">.icon-xinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinxi1"></span>
            <div class="name">
              信息13
            </div>
            <div class="code-name">.icon-xinxi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liuyan"></span>
            <div class="name">
              留言
            </div>
            <div class="code-name">.icon-liuyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wowowo"></span>
            <div class="name">
              我我我
            </div>
            <div class="code-name">.icon-wowowo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-female"></span>
            <div class="name">
              female
            </div>
            <div class="code-name">.icon-female
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-male"></span>
            <div class="name">
              male
            </div>
            <div class="code-name">.icon-male
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-genderless"></span>
            <div class="name">
              genderless
            </div>
            <div class="code-name">.icon-genderless
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Search"></span>
            <div class="name">
              Search
            </div>
            <div class="code-name">.icon-Search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chenggong"></span>
            <div class="name">
              成功
            </div>
            <div class="code-name">.icon-chenggong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-asterisk1"></span>
            <div class="name">
              asterisk
            </div>
            <div class="code-name">.icon-asterisk1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-notice"></span>
            <div class="name">
              notice
            </div>
            <div class="code-name">.icon-notice
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-topic"></span>
            <div class="name">
              topic
            </div>
            <div class="code-name">.icon-topic
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ask"></span>
            <div class="name">
              ask
            </div>
            <div class="code-name">.icon-ask
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-message"></span>
            <div class="name">
              message
            </div>
            <div class="code-name">.icon-message
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-avatar"></span>
            <div class="name">
              avatar
            </div>
            <div class="code-name">.icon-avatar
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-password"></span>
            <div class="name">
              password
            </div>
            <div class="code-name">.icon-password
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-github"></span>
            <div class="name">
              github
            </div>
            <div class="code-name">.icon-github
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lock"></span>
            <div class="name">
              lock
            </div>
            <div class="code-name">.icon-lock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.icon-lianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weizhi"></span>
            <div class="name">
              位置
            </div>
            <div class="code-name">.icon-weizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-erji"></span>
            <div class="name">
              耳机
            </div>
            <div class="code-name">.icon-erji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-article"></span>
            <div class="name">
              article
            </div>
            <div class="code-name">.icon-article
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-user"></span>
            <div class="name">
              user
            </div>
            <div class="code-name">.icon-user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-list1"></span>
            <div class="name">
              list
            </div>
            <div class="code-name">.icon-list1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-UserSettings"></span>
            <div class="name">
              User Settings
            </div>
            <div class="code-name">.icon-UserSettings
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-article1"></span>
            <div class="name">
              article
            </div>
            <div class="code-name">.icon-article1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-log-in"></span>
            <div class="name">
              log-in
            </div>
            <div class="code-name">.icon-log-in
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-register"></span>
            <div class="name">
              register
            </div>
            <div class="code-name">.icon-register
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-auto"></span>
            <div class="name">
              auto
            </div>
            <div class="code-name">.icon-auto
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-all"></span>
            <div class="name">
              all
            </div>
            <div class="code-name">.icon-all
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.icon-copy
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit"></use>
                </svg>
                <div class="name">edit</div>
                <div class="code-name">#icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yulan"></use>
                </svg>
                <div class="name">预览</div>
                <div class="code-name">#icon-yulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cai1"></use>
                </svg>
                <div class="name">踩1</div>
                <div class="code-name">#icon-cai1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cai2"></use>
                </svg>
                <div class="name">踩2</div>
                <div class="code-name">#icon-cai2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏1</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang1"></use>
                </svg>
                <div class="name">收藏2</div>
                <div class="code-name">#icon-shoucang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xin"></use>
                </svg>
                <div class="name">心1</div>
                <div class="code-name">#icon-xin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xin1"></use>
                </svg>
                <div class="name">心2</div>
                <div class="code-name">#icon-xin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zan"></use>
                </svg>
                <div class="name">赞1</div>
                <div class="code-name">#icon-zan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zan1"></use>
                </svg>
                <div class="name">赞2</div>
                <div class="code-name">#icon-zan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zan2"></use>
                </svg>
                <div class="name">赞4</div>
                <div class="code-name">#icon-zan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zan3"></use>
                </svg>
                <div class="name">赞5</div>
                <div class="code-name">#icon-zan3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinxi"></use>
                </svg>
                <div class="name">信息12</div>
                <div class="code-name">#icon-xinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinxi1"></use>
                </svg>
                <div class="name">信息13</div>
                <div class="code-name">#icon-xinxi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liuyan"></use>
                </svg>
                <div class="name">留言</div>
                <div class="code-name">#icon-liuyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wowowo"></use>
                </svg>
                <div class="name">我我我</div>
                <div class="code-name">#icon-wowowo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-female"></use>
                </svg>
                <div class="name">female</div>
                <div class="code-name">#icon-female</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-male"></use>
                </svg>
                <div class="name">male</div>
                <div class="code-name">#icon-male</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-genderless"></use>
                </svg>
                <div class="name">genderless</div>
                <div class="code-name">#icon-genderless</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Search"></use>
                </svg>
                <div class="name">Search</div>
                <div class="code-name">#icon-Search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chenggong"></use>
                </svg>
                <div class="name">成功</div>
                <div class="code-name">#icon-chenggong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-asterisk1"></use>
                </svg>
                <div class="name">asterisk</div>
                <div class="code-name">#icon-asterisk1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-notice"></use>
                </svg>
                <div class="name">notice</div>
                <div class="code-name">#icon-notice</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-topic"></use>
                </svg>
                <div class="name">topic</div>
                <div class="code-name">#icon-topic</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ask"></use>
                </svg>
                <div class="name">ask</div>
                <div class="code-name">#icon-ask</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-message"></use>
                </svg>
                <div class="name">message</div>
                <div class="code-name">#icon-message</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-avatar"></use>
                </svg>
                <div class="name">avatar</div>
                <div class="code-name">#icon-avatar</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-password"></use>
                </svg>
                <div class="name">password</div>
                <div class="code-name">#icon-password</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-github"></use>
                </svg>
                <div class="name">github</div>
                <div class="code-name">#icon-github</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lock"></use>
                </svg>
                <div class="name">lock</div>
                <div class="code-name">#icon-lock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#icon-lianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weizhi"></use>
                </svg>
                <div class="name">位置</div>
                <div class="code-name">#icon-weizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-erji"></use>
                </svg>
                <div class="name">耳机</div>
                <div class="code-name">#icon-erji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-article"></use>
                </svg>
                <div class="name">article</div>
                <div class="code-name">#icon-article</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user"></use>
                </svg>
                <div class="name">user</div>
                <div class="code-name">#icon-user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-list1"></use>
                </svg>
                <div class="name">list</div>
                <div class="code-name">#icon-list1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-UserSettings"></use>
                </svg>
                <div class="name">User Settings</div>
                <div class="code-name">#icon-UserSettings</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-article1"></use>
                </svg>
                <div class="name">article</div>
                <div class="code-name">#icon-article1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-log-in"></use>
                </svg>
                <div class="name">log-in</div>
                <div class="code-name">#icon-log-in</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-register"></use>
                </svg>
                <div class="name">register</div>
                <div class="code-name">#icon-register</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-auto"></use>
                </svg>
                <div class="name">auto</div>
                <div class="code-name">#icon-auto</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-all"></use>
                </svg>
                <div class="name">all</div>
                <div class="code-name">#icon-all</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#icon-copy</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
