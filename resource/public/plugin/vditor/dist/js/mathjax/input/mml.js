!function(r){var o={};function a(t){if(o[t])return o[t].exports;var e=o[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,a),e.l=!0,e.exports}a.m=r,a.c=o,a.d=function(t,e,r){a.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},a.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(a.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(r,o,function(t){return e[t]}.bind(null,o));return r},a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,"a",e),e},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.p="",a(a.s=10)}([function(t,e,r){"use strict";var o,a=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,a,i=r.call(t),n=[];try{for(;(void 0===e||0<e--)&&!(o=i.next()).done;)n.push(o.value)}catch(t){a={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return n};Object.defineProperty(e,"__esModule",{value:!0});var s,i=r(5),l=r(3),c=r(6),h=r(1),u=r(2),p=(s=i.AbstractInputJax,a(d,s),d.prototype.setAdaptor=function(t){s.prototype.setAdaptor.call(this,t),this.findMathML.adaptor=t,this.mathml.adaptor=t},d.prototype.setMmlFactory=function(t){s.prototype.setMmlFactory.call(this,t),this.mathml.setMmlFactory(t)},Object.defineProperty(d.prototype,"processStrings",{get:function(){return!1},enumerable:!0,configurable:!0}),d.prototype.compile=function(t,e){var r=t.start.node;if(!r||!t.end.node||this.options.forceReparse||"#text"===this.adaptor.kind(r)){var o=this.executeFilters(this.preFilters,t,e,t.math||"<math></math>"),a=this.checkForErrors(this.adaptor.parse(o,"text/"+this.options.parseAs)),i=this.adaptor.body(a);1!==this.adaptor.childNodes(i).length&&this.error("MathML must consist of a single element"),r=this.adaptor.remove(this.adaptor.firstChild(i)),"math"!==this.adaptor.kind(r).replace(/^[a-z]+:/,"")&&this.error("MathML must be formed by a <math> element, not <"+this.adaptor.kind(r)+">")}return r=this.executeFilters(this.mmlFilters,t,e,r),this.executeFilters(this.postFilters,t,e,this.mathml.compile(r))},d.prototype.checkForErrors=function(t){var e=this.adaptor.tags(this.adaptor.body(t),"parsererror")[0];return e&&(""===this.adaptor.textContent(e)&&this.error("Error processing MathML"),this.options.parseError.call(this,e)),t},d.prototype.error=function(t){throw new Error(t)},d.prototype.findMath=function(t){return this.findMathML.findMath(t)},d.NAME="MathML",d.OPTIONS=l.defaultOptions({parseAs:"html",forceReparse:!1,FindMathML:null,MathMLCompile:null,parseError:function(t){this.error(this.adaptor.textContent(t).replace(/\n.*/g,""))}},i.AbstractInputJax.OPTIONS),d);function d(t){void 0===t&&(t={});var e=this,r=n(l.separateOptions(t,h.FindMathML.OPTIONS,u.MathMLCompile.OPTIONS),3),o=r[0],a=r[1],i=r[2];return(e=s.call(this,o)||this).findMathML=e.options.FindMathML||new h.FindMathML(a),e.mathml=e.options.MathMLCompile||new u.MathMLCompile(i),e.mmlFilters=new c.FunctionList,e}e.MathML=p},function(t,e,r){"use strict";var o,a=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),f=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var i,n=r(7),M="http://www.w3.org/1998/Math/MathML",s=(i=n.AbstractFindMath,a(l,i),l.prototype.findMath=function(t){var e=new Set;this.findMathNodes(t,e),this.findMathPrefixed(t,e);var r=this.adaptor.root(this.adaptor.document);return"html"===this.adaptor.kind(r)&&0===e.size&&this.findMathNS(t,e),this.processMath(e)},l.prototype.findMathNodes=function(t,e){var r,o;try{for(var a=f(this.adaptor.tags(t,"math")),i=a.next();!i.done;i=a.next()){var n=i.value;e.add(n)}}catch(t){r={error:t}}finally{try{i&&!i.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}},l.prototype.findMathPrefixed=function(t,e){var r,o,a,i,n=this.adaptor.root(this.adaptor.document);try{for(var s=f(this.adaptor.allAttributes(n)),l=s.next();!l.done;l=s.next()){var c=l.value;if("xmlns:"===c.name.substr(0,6)&&c.value===M){var h=c.name.substr(6);try{for(var u=(a=void 0,f(this.adaptor.tags(t,h+":math"))),p=u.next();!p.done;p=u.next()){var d=p.value;e.add(d)}}catch(t){a={error:t}}finally{try{p&&!p.done&&(i=u.return)&&i.call(u)}finally{if(a)throw a.error}}}}}catch(t){r={error:t}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(r)throw r.error}}},l.prototype.findMathNS=function(t,e){var r,o;try{for(var a=f(this.adaptor.tags(t,"math",M)),i=a.next();!i.done;i=a.next()){var n=i.value;e.add(n)}}catch(t){r={error:t}}finally{try{i&&!i.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}},l.prototype.processMath=function(t){var e,r,o=[];try{for(var a=f(Array.from(t)),i=a.next();!i.done;i=a.next()){var n=i.value,s="block"===this.adaptor.getAttribute(n,"display")||"display"===this.adaptor.getAttribute(n,"mode"),l={node:n,n:0,delim:""},c={node:n,n:0,delim:""};o.push({math:this.adaptor.outerHTML(n),start:l,end:c,display:s})}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return o},l.OPTIONS={},l);function l(){return null!==i&&i.apply(this,arguments)||this}e.FindMathML=s},function(t,e,r){"use strict";var o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)},p=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var d=r(8),a=r(3),i=r(9),n=(s.prototype.setMmlFactory=function(t){this.factory=t},s.prototype.compile=function(t){var e=this.makeNode(t);return e.verifyTree(this.options.verify),e.setInheritedAttributes({},!1,0,!1),e.walkTree(this.markMrows),e},s.prototype.makeNode=function(t){var e,r,o=this.adaptor,a=!1,i=o.kind(t).replace(/^.*:/,""),n=o.getAttribute(t,"data-mjx-texclass")||"",s=n&&"mrow"===i?"TeXAtom":i;try{for(var l=p(o.allClasses(t)),c=l.next();!c.done;c=l.next()){var h=c.value;h.match(/^MJX-TeXAtom-/)?(n=h.substr(12),s="TeXAtom"):"MJX-fixedlimits"===h&&(a=!0)}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}this.factory.getNodeClass(s)||this.error('Unknown node type "'+s+'"');var u=this.factory.create(s);return"TeXAtom"===s?this.texAtom(u,n,a):n&&(u.texClass=d.TEXCLASS[n],u.setProperty("texClass",u.texClass)),this.addAttributes(u,t),this.checkClass(u,t),this.addChildren(u,t),u},s.prototype.addAttributes=function(t,e){var r,o,a=!1;try{for(var i=p(this.adaptor.allAttributes(e)),n=i.next();!n.done;n=i.next()){var s=n.value,l=s.name;if("data-mjx-"===l.substr(0,9))"data-mjx-alternate"===l?t.setProperty("variantForm",!0):"data-mjx-variant"===l&&(t.attributes.set("mathvariant",this.filterAttribute("mathvariant",s.value)),a=!0);else if("class"!==l){var c=this.filterAttribute(l,s.value);if(null!==c){var h=c.toLowerCase();"true"===h||"false"===h?t.attributes.set(l,"true"===h):a&&"mathvariant"===l||t.attributes.set(l,c)}}}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}},s.prototype.filterAttribute=function(t,e){return e},s.prototype.addChildren=function(t,e){var r,o;if(0!==t.arity){var a=this.adaptor;try{for(var i=p(a.childNodes(e)),n=i.next();!n.done;n=i.next()){var s=n.value,l=a.kind(s);if("#comment"!==l)if("#text"===l)this.addText(t,s);else if(t.isKind("annotation-xml"))t.appendChild(this.factory.create("XML").setXML(s,a));else{var c=t.appendChild(this.makeNode(s));0===c.arity&&a.childNodes(s).length&&(this.options.fixMisplacedChildren?this.addChildren(t,s):c.mError("There should not be children for "+c.kind+" nodes",this.options.verify,!0))}}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}}},s.prototype.addText=function(t,e){var r=this.adaptor.value(e);(t.isToken||t.getProperty("isChars"))&&t.arity?(t.isToken&&(r=i.translate(r),r=this.trimSpace(r)),t.appendChild(this.factory.create("text").setText(r))):r.match(/\S/)&&this.error('Unexpected text node "'+r+'"')},s.prototype.checkClass=function(t,e){var r,o,a=[];try{for(var i=p(this.adaptor.allClasses(e)),n=i.next();!n.done;n=i.next()){var s=n.value;"MJX-"===s.substr(0,4)?"MJX-variant"===s?t.setProperty("variantForm",!0):"MJX-TeXAtom"!==s.substr(0,11)&&t.attributes.set("mathvariant",this.fixCalligraphic(s.substr(3))):a.push(s)}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}a.length&&t.attributes.set("class",a.join(" "))},s.prototype.fixCalligraphic=function(t){return t.replace(/caligraphic/,"calligraphic")},s.prototype.texAtom=function(t,e,r){t.texClass=d.TEXCLASS[e],t.setProperty("texClass",t.texClass),"OP"!==e||r||(t.setProperty("movesupsub",!0),t.attributes.setInherited("movablelimits",!0))},s.prototype.markMrows=function(t){if(t.isKind("mrow")&&!t.isInferred&&2<=t.childNodes.length){var e=t.childNodes[0],r=t.childNodes[t.childNodes.length-1];e.isKind("mo")&&e.attributes.get("fence")&&r.isKind("mo")&&r.attributes.get("fence")&&(e.childNodes.length&&t.setProperty("open",e.getText()),r.childNodes.length&&t.setProperty("close",r.getText()))}},s.prototype.trimSpace=function(t){return t.replace(/[\t\n\r]/g," ").replace(/^ +/,"").replace(/ +$/,"").replace(/  +/g," ")},s.prototype.error=function(t){throw new Error(t)},s.OPTIONS={MmlFactory:null,fixMisplacedChildren:!0,verify:{},translateEntities:!0},s.VERIFY=o({},d.AbstractMmlNode.verifyDefaults),s);function s(t){void 0===t&&(t={});var e=this.constructor;this.options=a.userOptions(a.defaultOptions({},e.OPTIONS),t),this.options.verify&&(this.options.verify=a.userOptions(a.defaultOptions({},e.VERIFY),this.options.verify))}e.MathMLCompile=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractInputJax=MathJax._.core.InputJax.AbstractInputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FunctionList=MathJax._.util.FunctionList.FunctionList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFindMath=MathJax._.core.FindMath.AbstractFindMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.options=MathJax._.util.Entities.options,e.entities=MathJax._.util.Entities.entities,e.add=MathJax._.util.Entities.add,e.remove=MathJax._.util.Entities.remove,e.translate=MathJax._.util.Entities.translate,e.numeric=MathJax._.util.Entities.numeric},function(t,e,r){"use strict";r.r(e);var o=r(4),a=r(0),i=r(1),n=r(2);Object(o.combineWithMathJax)({_:{input:{mathml_ts:a,mathml:{FindMathML:i,MathMLCompile:n}}}}),MathJax.startup&&(MathJax.startup.registerConstructor("mml",a.MathML),MathJax.startup.useInput("mml"))}]);