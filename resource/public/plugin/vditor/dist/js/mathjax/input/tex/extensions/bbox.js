!function(a){var o={};function n(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return a[e].call(t.exports,t,t.exports,n),t.l=!0,t.exports}n.m=a,n.c=o,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(a,o,function(e){return t[e]}.bind(null,o));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=5)}([function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=MathJax._.components.global.isObject,t.combineConfig=MathJax._.components.global.combineConfig,t.combineDefaults=MathJax._.components.global.combineDefaults,t.combineWithMathJax=MathJax._.components.global.combineWithMathJax,t.MathJax=MathJax._.components.global.MathJax},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a(2),n=a(3),b=a(4);t.BboxMethods={},t.BboxMethods.BBox=function(e,t){for(var a,o,n,r=e.GetBrackets(t,""),i=e.ParseArg(t),u=r.split(/,/),l=0,c=u.length;l<c;l++){var p=u[l].trim(),f=p.match(/^(\.\d+|\d+(\.\d*)?)(pt|em|ex|mu|px|in|cm|mm)$/);if(f){if(a)throw new b.default("MultipleBBoxProperty","%1 specified twice in %2","Padding",t);var s=M(f[1]+f[3]);s&&(a={height:"+"+s,depth:"+"+s,lspace:s,width:"+"+2*parseInt(f[1],10)+f[3]})}else if(p.match(/^([a-z0-9]+|\#[0-9a-f]{6}|\#[0-9a-f]{3})$/i)){if(o)throw new b.default("MultipleBBoxProperty","%1 specified twice in %2","Background",t);o=p}else if(p.match(/^[-a-z]+:/i)){if(n)throw new b.default("MultipleBBoxProperty","%1 specified twice in %2","Style",t);n=d(p)}else if(""!==p)throw new b.default("InvalidBBoxProperty",'"%1" doesn\'t look like a color, a padding dimension, or a style',p)}a&&(i=e.create("node","mpadded",[i],a)),(o||n)&&(a={},o&&Object.assign(a,{mathbackground:o}),n&&Object.assign(a,{style:n}),i=e.create("node","mstyle",[i],a)),e.Push(i)};var d=function(e){return e},M=function(e){return e};new n.CommandMap("bbox",{bbox:"BBox"},t.BboxMethods),t.BboxConfiguration=o.Configuration.create("bbox",{handler:{macro:["bbox"]}})},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Configuration=MathJax._.input.tex.Configuration.Configuration,t.ConfigurationHandler=MathJax._.input.tex.Configuration.ConfigurationHandler},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractSymbolMap=MathJax._.input.tex.SymbolMap.AbstractSymbolMap,t.RegExpMap=MathJax._.input.tex.SymbolMap.RegExpMap,t.AbstractParseMap=MathJax._.input.tex.SymbolMap.AbstractParseMap,t.CharacterMap=MathJax._.input.tex.SymbolMap.CharacterMap,t.DelimiterMap=MathJax._.input.tex.SymbolMap.DelimiterMap,t.MacroMap=MathJax._.input.tex.SymbolMap.MacroMap,t.CommandMap=MathJax._.input.tex.SymbolMap.CommandMap,t.EnvironmentMap=MathJax._.input.tex.SymbolMap.EnvironmentMap},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=MathJax._.input.tex.TexError.default},function(e,t,a){"use strict";a.r(t);var o=a(0),n=a(1);Object(o.combineWithMathJax)({_:{input:{tex:{bbox:{BboxConfiguration:n}}}}})}]);