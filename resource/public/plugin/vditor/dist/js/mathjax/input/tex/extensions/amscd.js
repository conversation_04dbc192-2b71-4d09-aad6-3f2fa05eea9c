!function(a){var r={};function n(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return a[e].call(t.exports,t,t.exports,n),t.l=!0,t.exports}n.m=a,n.c=r,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(a,r,function(e){return t[e]}.bind(null,r));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=11)}([function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var _=a(7),x=a(8),C=a(9),v=a(10),g={CD:function(e,t){e.Push(t);var a=e.itemFactory.create("array"),r=e.configuration.options.amsCd;return a.setProperties({minw:e.stack.env.CD_minw||r.harrowsize,minh:e.stack.env.CD_minh||r.varrowsize}),a.arraydef={columnalign:"center",columnspacing:r.colspace,rowspacing:r.rowspace,displaystyle:!0},a},arrow:function(e,t){var a=e.string.charAt(e.i);if(!a.match(/[><VA.|=]/))return x.Other(e,t);e.i++;var r=e.stack.Top();r.isKind("array")&&!r.Size()||(g.cell(e,t),r=e.stack.Top());for(var n,o=r,i=o.table.length%2==1,l=(o.row.length+(i?0:1))%2;l;)g.cell(e,t),l--;var c={minsize:o.getProperty("minw"),stretchy:!0},s={minsize:o.getProperty("minh"),stretchy:!0,symmetric:!0,lspace:0,rspace:0};if("."!==a)if("|"===a)n=e.create("token","mo",s,"\u2225");else if("="===a)n=e.create("token","mo",c,"=");else{var u={">":"\u2192","<":"\u2190",V:"\u2193",A:"\u2191"}[a],d=e.GetUpTo(t+a,a),m=e.GetUpTo(t+a,a);if(">"===a||"<"===a){if(n=e.create("token","mo",c,u),(d=d||"\\kern "+o.getProperty("minw"))||m){var p={width:"+11mu",lspace:"6mu"};if(n=e.create("node","munderover",[n]),d){var M=new _.default(d,e.stack.env,e.configuration).mml(),f=e.create("node","mpadded",[M],p);v.default.setAttribute(f,"voffset",".1em"),v.default.setChild(n,n.over,f)}if(m){var h=new _.default(m,e.stack.env,e.configuration).mml();v.default.setChild(n,n.under,e.create("node","mpadded",[h],p))}e.configuration.options.amsCd.hideHorizontalLabels&&(n=e.create("node","mpadded",n,{depth:0,height:".67em"}))}}else{var b=e.create("token","mo",s,u);n=b,(d||m)&&(n=e.create("node","mrow"),d&&v.default.appendChildren(n,[new _.default("\\scriptstyle\\llap{"+d+"}",e.stack.env,e.configuration).mml()]),b.texClass=C.TEXCLASS.ORD,v.default.appendChildren(n,[b]),m&&v.default.appendChildren(n,[new _.default("\\scriptstyle\\rlap{"+m+"}",e.stack.env,e.configuration).mml()]))}}n&&e.Push(n),g.cell(e,t)},cell:function(e,t){var a=e.stack.Top();(a.table||[]).length%2==0&&0===(a.row||[]).length&&e.Push(e.create("node","mpadded",[],{height:"8.5pt",depth:"2pt"})),e.Push(e.itemFactory.create("cell").setProperties({isEntry:!0,name:t}))},minCDarrowwidth:function(e,t){e.stack.env.CD_minw=e.GetDimen(t)},minCDarrowheight:function(e,t){e.stack.env.CD_minh=e.GetDimen(t)}};t.default=g},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=MathJax._.components.global.isObject,t.combineConfig=MathJax._.components.global.combineConfig,t.combineDefaults=MathJax._.components.global.combineDefaults,t.combineWithMathJax=MathJax._.components.global.combineWithMathJax,t.MathJax=MathJax._.components.global.MathJax},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(3);a(4),t.AmsCdConfiguration=r.Configuration.create("amsCd",{handler:{character:["amsCd_special"],macro:["amsCd_macros"],environment:["amsCd_environment"]},options:{amsCd:{colspace:"5pt",rowspace:"5pt",harrowsize:"2.75em",varrowsize:"1.75em",hideHorizontalLabels:!1}}})},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Configuration=MathJax._.input.tex.Configuration.Configuration,t.ConfigurationHandler=MathJax._.input.tex.Configuration.ConfigurationHandler},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(5),n=a(6),o=a(0);new r.EnvironmentMap("amsCd_environment",n.default.environment,{CD:"CD"},o.default),new r.CommandMap("amsCd_macros",{minCDarrowwidth:"minCDarrowwidth",minCDarrowheight:"minCDarrowheight"},o.default),new r.MacroMap("amsCd_special",{"@":"arrow"},o.default)},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractSymbolMap=MathJax._.input.tex.SymbolMap.AbstractSymbolMap,t.RegExpMap=MathJax._.input.tex.SymbolMap.RegExpMap,t.AbstractParseMap=MathJax._.input.tex.SymbolMap.AbstractParseMap,t.CharacterMap=MathJax._.input.tex.SymbolMap.CharacterMap,t.DelimiterMap=MathJax._.input.tex.SymbolMap.DelimiterMap,t.MacroMap=MathJax._.input.tex.SymbolMap.MacroMap,t.CommandMap=MathJax._.input.tex.SymbolMap.CommandMap,t.EnvironmentMap=MathJax._.input.tex.SymbolMap.EnvironmentMap},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=MathJax._.input.tex.ParseMethods.default},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=MathJax._.input.tex.TexParser.default},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Other=MathJax._.input.tex.base.BaseConfiguration.Other,t.BaseTags=MathJax._.input.tex.base.BaseConfiguration.BaseTags,t.BaseConfiguration=MathJax._.input.tex.base.BaseConfiguration.BaseConfiguration},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,t.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,t.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,t.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,t.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,t.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,t.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,t.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,t.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,t.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=MathJax._.input.tex.NodeUtil.default},function(e,t,a){"use strict";a.r(t);var r=a(1),n=a(2),o=a(0);Object(r.combineWithMathJax)({_:{input:{tex:{ams_cd:{AmsCdConfiguration:n,AmsCdMethods:o}}}}})}]);