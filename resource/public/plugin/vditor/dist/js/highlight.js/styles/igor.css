.highlight-chroma{background-color:#fff}.highlight-chroma .highlight-lntd{vertical-align:top;padding:0;margin:0;border:0}.highlight-chroma .highlight-lntable{border-spacing:0;padding:0;margin:0;border:0;width:auto;overflow:auto;display:block}.highlight-chroma .highlight-hl{display:block;width:100%;background-color:#e5e5e5}.highlight-chroma .highlight-ln,.highlight-chroma .highlight-lnt{margin-right:.4em;padding:0 .4em;color:#7f7f7f}.highlight-chroma .highlight-k,.highlight-chroma .highlight-kc,.highlight-chroma .highlight-kd,.highlight-chroma .highlight-kn,.highlight-chroma .highlight-kp,.highlight-chroma .highlight-kr,.highlight-chroma .highlight-kt{color:#00f}.highlight-chroma .highlight-nc{color:#007575}.highlight-chroma .highlight-nd{color:#cc00a3}.highlight-chroma .highlight-nf{color:#c34e00}.highlight-chroma .highlight-dl,.highlight-chroma .highlight-s,.highlight-chroma .highlight-s1,.highlight-chroma .highlight-s2,.highlight-chroma .highlight-sa,.highlight-chroma .highlight-sb,.highlight-chroma .highlight-sc,.highlight-chroma .highlight-sd,.highlight-chroma .highlight-se,.highlight-chroma .highlight-sh,.highlight-chroma .highlight-si,.highlight-chroma .highlight-sr,.highlight-chroma .highlight-ss,.highlight-chroma .highlight-sx{color:#009c00}.highlight-chroma .highlight-c,.highlight-chroma .highlight-c1,.highlight-chroma .highlight-ch,.highlight-chroma .highlight-cm,.highlight-chroma .highlight-cp,.highlight-chroma .highlight-cpf,.highlight-chroma .highlight-cs{color:red;font-style:italic}.hljs{display:block;overflow-x:auto;padding:.5em;background:#fff}.hljs,.hljs-emphasis,.hljs-strong,.hljs-subst,.hljs-tag,.hljs-title{color:#000}.hljs-bullet,.hljs-literal,.hljs-number,.hljs-quote,.hljs-regexp{color:navy}.hljs-code .hljs-selector-class{color:purple}.hljs-emphasis,.hljs-stronge,.hljs-type{font-style:italic}.hljs-function,.hljs-keyword,.hljs-name,.hljs-section,.hljs-selector-tag,.hljs-symbol{color:olive}.hljs-attribute{color:maroon}.hljs-class .hljs-title,.hljs-params,.hljs-variable{color:#0055af}.hljs-addition,.hljs-built_in,.hljs-builtin-name,.hljs-comment,.hljs-deletion,.hljs-link,.hljs-meta,.hljs-selector-attr,.hljs-selector-id,.hljs-selector-pseudo,.hljs-string,.hljs-template-tag,.hljs-template-variable,.hljs-type{color:green}