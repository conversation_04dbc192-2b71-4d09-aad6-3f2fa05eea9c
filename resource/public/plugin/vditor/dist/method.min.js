/*!
 * Vditor v3.7.1 - A markdown editor written in TypeScript.
 *   
 * MIT License
 * 
 * Copyright (c) 2018-present B3log 开源, b3log.org
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 * 
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Vditor=t():e.Vditor=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=24)}([function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"h",(function(){return l})),n.d(t,"c",(function(){return d})),n.d(t,"g",(function(){return u})),n.d(t,"f",(function(){return f})),n.d(t,"d",(function(){return m}));var r=n(2),i=n(3),o=n(1),a=function(e){var t;return getSelection().rangeCount>0&&(t=getSelection().getRangeAt(0),e.isEqualNode(t.startContainer)||e.contains(t.startContainer))||(e.focus(),(t=e.ownerDocument.createRange()).setStart(e,0),t.collapse(!0)),t},c=function(e){var t=window.getSelection().getRangeAt(0);if(!e.contains(t.startContainer)&&!Object(o.e)(t.startContainer,"vditor-panel--none"))return{left:0,top:0};var n,r=e.parentElement.getBoundingClientRect();if(0===t.getClientRects().length)if(3===t.startContainer.nodeType){var i=t.startContainer.parentElement;if(!(i&&i.getClientRects().length>0))return{left:0,top:0};n=i.getClientRects()[0]}else{var a=t.startContainer.children;if(a[t.startOffset]&&a[t.startOffset].getClientRects().length>0)n=a[t.startOffset].getClientRects()[0];else if(t.startContainer.childNodes.length>0){var c=t.cloneRange();t.selectNode(t.startContainer.childNodes[Math.max(0,t.startOffset-1)]),n=t.getClientRects()[0],t.setEnd(c.endContainer,c.endOffset),t.setStart(c.startContainer,c.startOffset)}else n=t.startContainer.getClientRects()[0];if(!n){for(var s=t.startContainer.childNodes[t.startOffset];!s.getClientRects||s.getClientRects&&0===s.getClientRects().length;)s=s.parentElement;n=s.getClientRects()[0]}}else n=t.getClientRects()[0];return{left:n.left-r.left,top:n.top-r.top}},s=function(e,t){if(!t){if(0===getSelection().rangeCount)return!1;t=getSelection().getRangeAt(0)}var n=t.commonAncestorContainer;return e.isEqualNode(n)||e.contains(n)},l=function(e){var t=window.getSelection();t.removeAllRanges(),t.addRange(e)},d=function(e,t,n){var r={end:0,start:0};if(!n){if(0===getSelection().rangeCount)return r;n=window.getSelection().getRangeAt(0)}if(s(t,n)){var i=n.cloneRange();e.childNodes[0]&&e.childNodes[0].childNodes[0]?i.setStart(e.childNodes[0].childNodes[0],0):i.selectNodeContents(e),i.setEnd(n.startContainer,n.startOffset),r.start=i.toString().length,r.end=r.start+n.toString().length}return r},u=function(e,t,n){var r=0,i=0,o=n.childNodes[i],a=!1,c=!1;e=Math.max(0,e),t=Math.max(0,t);var s=n.ownerDocument.createRange();for(s.setStart(o||n,0),s.collapse(!0);!c&&o;){var d=r+o.textContent.length;if(!a&&e>=r&&e<=d&&(0===e?s.setStart(o,0):3===o.childNodes[0].nodeType?s.setStart(o.childNodes[0],e-r):o.nextSibling?s.setStartBefore(o.nextSibling):s.setStartAfter(o),a=!0,e===t)){c=!0;break}a&&t>=r&&t<=d&&(0===t?s.setEnd(o,0):3===o.childNodes[0].nodeType?s.setEnd(o.childNodes[0],t-r):o.nextSibling?s.setEndBefore(o.nextSibling):s.setEndAfter(o),c=!0),r=d,o=n.childNodes[++i]}return!c&&n.childNodes[i-1]&&s.setStartBefore(n.childNodes[i-1]),l(s),s},f=function(e,t){var n=e.querySelector("wbr");if(n){if(n.previousElementSibling)if(n.previousElementSibling.isSameNode(n.previousSibling)){if(n.previousElementSibling.lastChild)return t.setStartBefore(n),t.collapse(!0),l(t),!Object(i.c)()||"EM"!==n.previousElementSibling.tagName&&"STRONG"!==n.previousElementSibling.tagName&&"S"!==n.previousElementSibling.tagName||(t.insertNode(document.createTextNode(r.a.ZWSP)),t.collapse(!1)),void n.remove();t.setStartAfter(n.previousElementSibling)}else t.setStart(n.previousSibling,n.previousSibling.textContent.length);else n.previousSibling?t.setStart(n.previousSibling,n.previousSibling.textContent.length):n.nextSibling?3===n.nextSibling.nodeType?t.setStart(n.nextSibling,0):t.setStartBefore(n.nextSibling):t.setStart(n.parentElement,0);t.collapse(!0),n.remove(),l(t)}},m=function(e,t){var n=document.createElement("div");n.innerHTML=e;var r=n.querySelectorAll("p");1===r.length&&!r[0].previousSibling&&!r[0].nextSibling&&t[t.currentMode].element.children.length>0&&"P"===n.firstElementChild.tagName&&(e=r[0].innerHTML.trim());var i=document.createElement("div");i.innerHTML=e;var c=a(t[t.currentMode].element);if(""!==c.toString()&&(t[t.currentMode].preventInput=!0,document.execCommand("delete",!1,"")),i.firstElementChild&&"0"===i.firstElementChild.getAttribute("data-block")){i.lastElementChild.insertAdjacentHTML("beforeend","<wbr>");var s=Object(o.c)(c.startContainer);s?s.insertAdjacentHTML("afterend",i.innerHTML):t[t.currentMode].element.insertAdjacentHTML("beforeend",i.innerHTML),f(t[t.currentMode].element,c)}else{var d=document.createElement("template");d.innerHTML=e,c.insertNode(d.content.cloneNode(!0)),c.collapse(!1),l(c)}}},function(e,t,n){"use strict";n.d(t,"g",(function(){return i})),n.d(t,"h",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"d",(function(){return c})),n.d(t,"c",(function(){return s})),n.d(t,"f",(function(){return l})),n.d(t,"e",(function(){return d})),n.d(t,"a",(function(){return u}));var r=n(6),i=function(e,t){for(var n=d(e,t),r=!1,i=!1;n&&!n.classList.contains("vditor-reset")&&!i;)(r=d(n.parentElement,t))?n=r:i=!0;return n||!1},o=function(e,t){for(var n=Object(r.b)(e,t),i=!1,o=!1;n&&!n.classList.contains("vditor-reset")&&!o;)(i=Object(r.b)(n.parentElement,t))?n=i:o=!0;return n||!1},a=function(e){var t=o(e,"UL"),n=o(e,"OL"),r=t;return n&&(!t||t&&n.contains(t))&&(r=n),r},c=function(e,t,n){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var r=e,i=!1;r&&!i&&!r.classList.contains("vditor-reset");)r.getAttribute(t)===n?i=!0:r=r.parentElement;return i&&r},s=function(e){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);var t=e,n=!1,r=c(e,"data-block","0");if(r)return r;for(;t&&!n&&!t.classList.contains("vditor-reset");)"H1"===t.tagName||"H2"===t.tagName||"H3"===t.tagName||"H4"===t.tagName||"H5"===t.tagName||"H6"===t.tagName||"P"===t.tagName||"BLOCKQUOTE"===t.tagName||"OL"===t.tagName||"UL"===t.tagName?n=!0:t=t.parentElement;return n&&t},l=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,r=!1;n&&!r&&!n.classList.contains("vditor-reset");)n.nodeName===t?r=!0:n=n.parentElement;return r&&n},d=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,r=!1;n&&!r&&!n.classList.contains("vditor-reset");)n.classList.contains(t)?r=!0:n=n.parentElement;return r&&n},u=function(e){for(;e&&e.lastChild;)e=e.lastChild;return e}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="3.7.1",i=function(){function e(){}return e.ZWSP="​",e.MOBILE_WIDTH=520,e.CLASS_MENU_DISABLED="vditor-menu--disabled",e.EDIT_TOOLBARS=["emoji","headings","bold","italic","strike","link","list","ordered-list","outdent","indent","check","line","quote","code","inline-code","insert-after","insert-before","upload","record","table"],e.CODE_THEME=["abap","algol","algol_nu","arduino","autumn","borland","bw","colorful","dracula","emacs","friendly","fruity","github","igor","lovelace","manni","monokai","monokailight","murphy","native","paraiso-dark","paraiso-light","pastie","perldoc","pygments","rainbow_dash","rrt","solarized-dark","solarized-dark256","solarized-light","swapoff","tango","trac","vim","vs","xcode","ant-design"],e.CODE_LANGUAGES=["mermaid","echarts","mindmap","abc","graphviz","flowchart","apache","bash","cs","cpp","css","coffeescript","diff","xml","http","ini","json","java","javascript","js","makefile","markdown","nginx","objectivec","php","perl","properties","python","ruby","sql","shell","dart","erb","go","gradle","julia","kotlin","less","lua","matlab","rust","scss","typescript","ts","yaml"],e.CDN="https://cdn.jsdelivr.net/npm/vditor@3.7.1",e.MARKDOWN_OPTIONS={autoSpace:!1,chinesePunct:!1,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",linkPrefix:"",listStyle:!1,mark:!1,mathBlockPreview:!0,paragraphBeginningSpace:!1,sanitize:!0,toc:!1},e.HLJS_OPTIONS={enable:!0,lineNumber:!1,style:"github"},e.MATH_OPTIONS={engine:"KaTeX",inlineDigit:!1,macros:{}},e.THEME_OPTIONS={current:"light",list:{"ant-design":"Ant Design",dark:"Dark",light:"Light",wechat:"WeChat"},path:e.CDN+"/dist/css/content-theme"},e}()},function(e,t,n){"use strict";n.d(t,"f",(function(){return r})),n.d(t,"e",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"d",(function(){return c})),n.d(t,"g",(function(){return s})),n.d(t,"c",(function(){return l}));var r=function(){return navigator.userAgent.indexOf("Safari")>-1&&-1===navigator.userAgent.indexOf("Chrome")},i=function(){return navigator.userAgent.toLowerCase().indexOf("firefox")>-1},o=function(){try{return"undefined"!=typeof localStorage}catch(e){return!1}},a=function(){return navigator.userAgent.indexOf("iPhone")>-1?"touchstart":"click"},c=function(e){return navigator.platform.toUpperCase().indexOf("MAC")>=0?!(!e.metaKey||e.ctrlKey):!(e.metaKey||!e.ctrlKey)},s=function(e){return/Mac/.test(navigator.platform)||"iPhone"===navigator.platform?(e=e.replace("ctrl","⌘").replace("shift","⇧").replace("alt","⌥")).indexOf("⇧")>-1&&(e=i()?e.replace(";",":").replace("=","+"):e.replace(":",";").replace("+","=").replace("_","-")):(e=e.replace("⌘","ctrl").replace("⇧","shift").replace("⌥","alt")).indexOf("shift")>-1&&(e=e.replace(";",":").replace("=","+")),e},l=function(){return/Chrome/.test(navigator.userAgent)&&/Google Inc/.test(navigator.vendor)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={en_US:{alignCenter:"Center",alignLeft:"Left",alignRight:"Right",alternateText:"Alternate text",bold:"Blod",both:"editor & preview",check:"Task List",code:"Code Block","code-theme":"Code Block Theme Preview",column:"Column",comment:"Comment",confirm:"Confirm","content-theme":"Content Theme Preview",copied:"Copied",copy:"Copy","delete-column":"Delete Row","delete-row":"Delete Column",devtools:"DevTools",down:"Down",downloadTip:"The browser does not support the download function",edit:"Edit","edit-mode":"Toggle Edit Mode",emoji:"Emoji",export:"Export",fileTypeError:"file type is error",footnoteRef:"Footnote Ref",fullscreen:"Toggle Fullscreen",generate:"Generating",headings:"Headings",help:"Help",imageURL:"image URL",indent:"Indent",info:"Info","inline-code":"Inline Code","insert-after":"Insert line after","insert-before":"Insert line Before","insert-column":"Insert Column","insert-row":"Insert Row",instantRendering:"Instant Rendering",italic:"Italic",language:"Language",line:"Line",link:"Link",linkRef:"Link Ref",list:"List",more:"More",nameEmpty:"Name is empty","ordered-list":"Order List",outdent:"Outdent",outline:"Outline",over:"over",performanceTip:"Real-time preview requires ${x}ms, you can close it",preview:"Preview",quote:"Quote",record:"Start Record/End Record","record-tip":"The device does not support recording",recording:"recording...",redo:"Redo",remove:"Remove",row:"Row",splitView:"Split View",strike:"Strike",table:"Table",textIsNotEmpty:"text(no empty)",title:"Title",tooltipText:"Tooltip text",undo:"Undo",up:"Up",update:"Update",upload:"Upload image or file",uploadError:"upload error",uploading:"uploading...",wysiwyg:"WYSIWYG"},ja_JP:{alignCenter:"中央",alignLeft:"左側",alignRight:"右側",alternateText:"イメージタグ",bold:"太く",both:"エディター & プレビュー",check:"チェックリスト",code:"コードブロック挿入","code-theme":"コードブロックテーマ",column:"行列",comment:"コメント",confirm:"確認","content-theme":"コンテンツテーマ",copied:"コピー完了",copy:"コピー","delete-column":"列 消去","delete-row":"行 消去",devtools:"開発ツール",down:"ダウンロード",downloadTip:"ブラウザがダウンロード機能をサポートしていません。",edit:"修正","edit-mode":"編集モード",emoji:"絵文字",export:"書き出し",fileTypeError:"サポートしていません。",footnoteRef:"脚注参照",fullscreen:"全体画面",generate:"作成する",headings:"タイトル大きさ",help:"ヘルプ",imageURL:"イメージ URL",indent:"字下げ",info:"情報","inline-code":"インラインコード","insert-after":"ブロックの後ろに入力","insert-before":"ブロックの前に入力","insert-column":"列 挿入","insert-row":"行 挿入",instantRendering:"インスタントレンダリング",italic:"斜体",language:"言語",line:"段落分割",link:"リンク",linkRef:"リンク参照",list:"リスト",more:"詳しく見る",nameEmpty:"名前が入力されていません。","ordered-list":"順序のあるリスト",outdent:"ぶら下げインデント",outline:"概要",over:"オーバー",performanceTip:"リアルタイムプレビューには、${x}msが必要でエディター/プレビューボタンをクリックして閉じる事が出来ます。",preview:"プレビュー",quote:"引用段落",record:"録音開始/録音終了","record-tip":"録音がサポートされていません。",recording:"録音中...",redo:"戻る",remove:"消去",row:"列",splitView:"マークダウン",strike:"取り消し線",table:"表 挿入",textIsNotEmpty:"テキスト(no empty)",title:"題名",tooltipText:"ツールチップ",undo:"取り消す",up:"戻る",update:"アップデート",upload:"イメージをダウンロードする",uploadError:"アップロード失敗",uploading:"アップロード中",wysiwyg:"ウィジウィグ"},ko_KR:{alignCenter:"가운데",alignLeft:"왼쪽",alignRight:"오른쪽",alternateText:"이미지 태그",bold:"굵게",both:"에디터 & 미리보기",check:"체크박스",code:"코드블럭삽입","code-theme":"코드블럭테마",column:"행",comment:"코멘트",confirm:"확인","content-theme":"컨텐츠테마",copied:"복사완료",copy:"복사","delete-column":"열 삭제","delete-row":"행 삭제",devtools:"개발툴",down:"다운",downloadTip:"브라우저가 다운로드 기능을 지원하지 않습니다",edit:"수정","edit-mode":"편집모드",emoji:"이모지",export:"내보내기",fileTypeError:"지원하지않습니다.",footnoteRef:"각주참조",fullscreen:"전체화면",generate:"생성",headings:"제목크기",help:"도움말",imageURL:"이미지 URL",indent:"들여쓰기",info:"정보","inline-code":"인라인코드","insert-after":"블락 뒤로 입력","insert-before":"블락 앞으로 입력","insert-column":"열 삽입","insert-row":"행 삽입",instantRendering:"타이포라",italic:"기울임꼴",language:"언어",line:"문단나눔",link:"링크",linkRef:"링크 참조",list:"순서없는 목록",more:"더보기",nameEmpty:"이름이 비어있습니다.","ordered-list":"순서있는 목록",outdent:"내어쓰기",outline:"개요",over:"오버",performanceTip:"실시간 미리보기에는 ${x}ms가 필요하며 에디터/미리보기 버튼을 클릭하여 닫을 수 있습니다.",preview:"미리보기",quote:"인용단락",record:"녹음시작/녹음종료","record-tip":"녹음을 지원하지 않습니다.",recording:"녹음중...",redo:"되돌리기",remove:"삭제",row:"열",splitView:"마크다운",strike:"취소선",table:"표삽입",textIsNotEmpty:"텍스트(no empty)",title:"표제",tooltipText:"툴팁",undo:"취소하기",up:"위로",update:"업데이트",upload:"이미지 업로드하기",uploadError:"업로드 실패",uploading:"업로드중...",wysiwyg:"위지위그"},zh_CN:{alignCenter:"居中",alignLeft:"居左",alignRight:"居右",alternateText:"替代文本",bold:"粗体",both:"编辑 & 预览",check:"任务列表",code:"代码块","code-theme":"代码块主题预览",column:"列",comment:"评论",confirm:"确定","content-theme":"内容主题预览",copied:"已复制",copy:"复制","delete-column":"删除列","delete-row":"删除行",devtools:"开发者工具",down:"下",downloadTip:"该浏览器不支持下载功能",edit:"编辑","edit-mode":"切换编辑模式",emoji:"表情",export:"导出",fileTypeError:"文件类型不允许上传",footnoteRef:"脚注标识",fullscreen:"全屏切换",generate:"生成中",headings:"标题",help:"帮助",imageURL:"图片地址",indent:"列表缩进",info:"关于","inline-code":"行内代码","insert-after":"末尾插入行","insert-before":"起始插入行","insert-column":"插入列","insert-row":"插入行",instantRendering:"即时渲染",italic:"斜体",language:"语言",line:"分隔线",link:"链接",linkRef:"引用标识",list:"无序列表",more:"更多",nameEmpty:"文件名不能为空","ordered-list":"有序列表",outdent:"列表反向缩进",outline:"大纲",over:"超过",performanceTip:"实时预览需 ${x}ms，可点击编辑 & 预览按钮进行关闭",preview:"预览",quote:"引用",record:"开始录音/结束录音","record-tip":"该设备不支持录音功能",recording:"录音中...",redo:"重做",remove:"删除",row:"行",splitView:"分屏预览",strike:"删除线",table:"表格",textIsNotEmpty:"文本（不能为空）",title:"标题",tooltipText:"提示文本",undo:"撤销",up:"上",update:"更新",upload:"上传图片或文件",uploadError:"上传错误",uploading:"上传中...",wysiwyg:"所见即所得"}}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r=function(e,t){if(document.getElementById(t))return!1;var n=new XMLHttpRequest;n.open("GET",e,!1),n.setRequestHeader("Accept","text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01"),n.send("");var r=document.createElement("script");r.type="text/javascript",r.text=n.responseText,r.id=t,document.head.appendChild(r)},i=function(e,t){return new Promise((function(n,r){if(document.getElementById(t))return n(),!1;var i=document.createElement("script");i.src=e,i.async=!0,document.head.appendChild(i),i.onload=function(){if(document.getElementById(t))return i.remove(),n(),!1;i.id=t,n()}}))}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,r=!1;n&&!r&&!n.classList.contains("vditor-reset");)0===n.nodeName.indexOf(t)?r=!0:n=n.parentElement;return r&&n},i=function(e){var t=r(e,"H");return!(!t||2!==t.tagName.length||"HR"===t.tagName)&&t}},function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var r=n(2),i=n(5),o=n(8),a=n(9),c=function(e,t){var n=e.querySelectorAll(".language-math");if(0!==n.length){var c={cdn:r.a.CDN,math:{engine:"KaTeX",inlineDigit:!1,macros:{}}};if(t&&t.math&&(t.math=Object.assign({},c.math,t.math)),"KaTeX"===(t=Object.assign({},c,t)).math.engine)Object(o.a)(t.cdn+"/dist/js/katex/katex.min.css","vditorKatexStyle"),Object(i.a)(t.cdn+"/dist/js/katex/katex.min.js","vditorKatexScript").then((function(){n.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")&&!e.getAttribute("data-math")){var t=Object(a.a)(e.textContent);e.setAttribute("data-math",t);try{e.innerHTML=katex.renderToString(t,{displayMode:"DIV"===e.tagName,output:"html"})}catch(t){e.innerHTML=t.message,e.className="vditor-reset--error"}e.addEventListener("copy",(function(e){e.stopPropagation(),e.preventDefault();var t=e.currentTarget.closest(".language-math");e.clipboardData.setData("text/html",t.innerHTML),e.clipboardData.setData("text/plain",t.getAttribute("data-math"))}))}}))}));else if("MathJax"===t.math.engine){window.MathJax||(window.MathJax={loader:{paths:{mathjax:t.cdn+"/dist/js/mathjax"}},tex:{macros:t.math.macros}}),Object(i.b)(t.cdn+"/dist/js/mathjax/tex-svg.js","vditorMathJaxScript");var s=function(e,t){var n=Object(a.a)(e.textContent).trim(),r=window.MathJax.getMetricsFor(e);r.display="DIV"===e.tagName,window.MathJax.tex2svgPromise(n,r).then((function(r){e.innerHTML="",e.setAttribute("data-math",n),e.append(r),window.MathJax.startup.document.clear(),window.MathJax.startup.document.updateDocument();var i=r.querySelector('[data-mml-node="merror"]');i&&""!==i.textContent.trim()&&(e.innerHTML=i.textContent.trim(),e.className="vditor-reset--error"),t&&t()}))};window.MathJax.startup.promise.then((function(){for(var e=[],t=function(t){var r=n[t];r.parentElement.classList.contains("vditor-wysiwyg__pre")||r.parentElement.classList.contains("vditor-ir__marker--pre")||r.getAttribute("data-math")||!Object(a.a)(r.textContent).trim()||e.push((function(e){t===n.length-1?s(r):s(r,e)}))},r=0;r<n.length;r++)t(r);!function(e){if(0!==e.length){var t=0,n=e[e.length-1],r=function(){var i=e[t++];i===n?i():i(r)};r()}}(e)}))}}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e,t){if(!document.getElementById(t)){var n=document.createElement("link");n.id=t,n.rel="stylesheet",n.type="text/css",n.href=e,document.getElementsByTagName("head")[0].appendChild(n)}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return e.replace(/\u00a0/g," ")}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(8),i=function(e,t){if(e&&t){var n=document.getElementById("vditorContentTheme"),i=t+"/"+e+".css";n?n.href!==i&&(n.remove(),Object(r.a)(i,"vditorContentTheme")):Object(r.a)(i,"vditorContentTheme")}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===e&&(e=document),void 0===t&&(t=r.a.CDN);var n=e.querySelectorAll(".language-abc");n.length>0&&Object(i.a)(t+"/dist/js/abcjs/abcjs_basic.min.js","vditorAbcjsScript").then((function(){n.forEach((function(e){e.parentElement.classList.contains("vditor-wysiwyg__pre")||e.parentElement.classList.contains("vditor-ir__marker--pre")||"true"!==e.getAttribute("data-processed")&&(ABCJS.renderAbc(e,e.textContent.trim()),e.style.overflowX="auto",e.setAttribute("data-processed","true"))}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t,n){void 0===e&&(e=document),void 0===t&&(t=r.a.CDN);var o=e.querySelectorAll(".language-echarts");o.length>0&&Object(i.a)(t+"/dist/js/echarts/echarts.min.js","vditorEchartsScript").then((function(){o.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")){var t=e.innerText.trim();if(t)try{if("true"===e.getAttribute("data-processed"))return;var r=JSON.parse(t);echarts.init(e,"dark"===n?"dark":void 0).setOption(r),e.setAttribute("data-processed","true")}catch(t){e.className="vditor-reset--error",e.innerHTML="echarts render error: <br>"+t}}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(4),i=n(9),o=function(e,t){void 0===t&&(t="zh_CN"),e.querySelectorAll("pre > code").forEach((function(n,o){if(!n.parentElement.classList.contains("vditor-wysiwyg__pre")&&!n.parentElement.classList.contains("vditor-ir__marker--pre")&&!(n.classList.contains("language-mermaid")||n.classList.contains("language-flowchart")||n.classList.contains("language-echarts")||n.classList.contains("language-mindmap")||n.classList.contains("language-abc")||n.classList.contains("language-graphviz")||n.classList.contains("language-math")||n.style.maxHeight.indexOf("px")>-1||e.classList.contains("vditor-preview")&&o>5)){var a=n.innerText;if(n.classList.contains("highlight-chroma")){var c=document.createElement("code");c.innerHTML=n.innerHTML,c.querySelectorAll(".highlight-ln").forEach((function(e){e.remove()})),a=c.innerText}var s=document.createElement("div");s.className="vditor-copy",s.innerHTML='<span aria-label="'+r.a[t].copy+"\"\nonmouseover=\"this.setAttribute('aria-label', '"+r.a[t].copy+"')\"\nclass=\"vditor-tooltipped vditor-tooltipped__w\"\nonclick=\"this.previousElementSibling.select();document.execCommand('copy');this.setAttribute('aria-label', '"+r.a[t].copied+'\')"><svg><use xlink:href="#vditor-icon-copy"></use></svg></span>';var l=document.createElement("textarea");l.value=Object(i.a)(a),s.insertAdjacentElement("afterbegin",l),n.before(s),n.style.maxHeight=window.outerHeight-40+"px"}}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===t&&(t=r.a.CDN);var n=e.querySelectorAll(".language-flowchart");0!==n.length&&Object(i.a)(t+"/dist/js/flowchart.js/flowchart.min.js","vditorFlowchartScript").then((function(){n.forEach((function(e){if("true"!==e.getAttribute("data-processed")){var t=flowchart.parse(e.textContent);e.innerHTML="",t.drawSVG(e),e.setAttribute("data-processed","true")}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===t&&(t=r.a.CDN);var n=e.querySelectorAll(".language-graphviz");0!==n.length&&Object(i.a)(t+"/dist/js/graphviz/viz.js","vditorGraphVizScript").then((function(){n.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")&&"true"!==e.getAttribute("data-processed")){try{var t=new Blob(["importScripts('"+document.getElementById("vditorGraphVizScript").src.replace("viz.js","full.render.js")+"');"],{type:"application/javascript"}),n=(window.URL||window.webkitURL).createObjectURL(t),r=new Worker(n);new Viz({worker:r}).renderSVGElement(e.textContent).then((function(t){e.innerHTML=t.outerHTML})).catch((function(t){e.innerHTML="graphviz render error: <br>"+t,e.className="vditor-reset--error"}))}catch(e){console.error("graphviz error",e)}e.setAttribute("data-processed","true")}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(2),i=n(5),o=n(8),a=function(e,t,n){void 0===t&&(t=document),void 0===n&&(n=r.a.CDN);var a=e.style;r.a.CODE_THEME.includes(a)||(a="github");var c=document.getElementById("vditorHljsStyle"),s=n+"/dist/js/highlight.js/styles/"+a+".css";(c&&c.href!==s&&c.remove(),Object(o.a)(n+"/dist/js/highlight.js/styles/"+a+".css","vditorHljsStyle"),!1!==e.enable)&&(0!==t.querySelectorAll("pre > code").length&&Object(i.a)(n+"/dist/js/highlight.js/highlight.pack.js","vditorHljsScript").then((function(){t.querySelectorAll("pre > code").forEach((function(t){if(!t.parentElement.classList.contains("vditor-ir__marker--pre")&&!t.parentElement.classList.contains("vditor-wysiwyg__pre")&&!(t.classList.contains("language-mermaid")||t.classList.contains("language-flowchat")||t.classList.contains("language-echarts")||t.classList.contains("language-mindmap")||t.classList.contains("language-abc")||t.classList.contains("language-graphviz")||t.classList.contains("language-math"))&&(hljs.highlightBlock(t),e.lineNumber)){t.classList.add("vditor-linenumber");var n=t.querySelector(".vditor-linenumber__temp");n||((n=document.createElement("div")).className="vditor-linenumber__temp",t.insertAdjacentElement("beforeend",n));var r=getComputedStyle(t).whiteSpace,i=!1;"pre-wrap"!==r&&"pre-line"!==r||(i=!0);var o="",a=t.textContent.split(/\r\n|\r|\n/g);a.pop(),a.map((function(e){var t="";i&&(n.textContent=e||"\n",t=' style="height:'+n.getBoundingClientRect().height+'px"'),o+="<span"+t+"></span>"})),n.style.display="none",o='<span class="vditor-linenumber__rows">'+o+"</span>",t.insertAdjacentHTML("beforeend",o)}}))})))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t,n){void 0===t&&(t=r.a.CDN);var o=e.querySelectorAll(".language-mermaid");0!==o.length&&Object(i.a)(t+"/dist/js/mermaid/mermaid.min.js","vditorMermaidScript").then((function(){var e={altFontFamily:"sans-serif",flowchart:{htmlLabels:!0,useMaxWidth:!0},fontFamily:"sans-serif",gantt:{leftPadding:75,rightPadding:20},securityLevel:"loose",sequence:{boxMargin:8,diagramMarginX:8,diagramMarginY:8,useMaxWidth:!0},startOnLoad:!1};"dark"===n&&(e.theme="dark",e.themeVariables={activationBkgColor:"hsl(180, 1.5873015873%, 28.3529411765%)",activationBorderColor:"#81B1DB",activeTaskBkgColor:"#81B1DB",activeTaskBorderColor:"#ffffff",actorBkg:"#1f2020",actorBorder:"#81B1DB",actorLineColor:"lightgrey",actorTextColor:"lightgrey",altBackground:"hsl(0, 0%, 40%)",altSectionBkgColor:"#333",arrowheadColor:"lightgrey",background:"#333",border1:"#81B1DB",border2:"rgba(255, 255, 255, 0.25)",classText:"#e0dfdf",clusterBkg:"hsl(180, 1.5873015873%, 28.3529411765%)",clusterBorder:"rgba(255, 255, 255, 0.25)",critBkgColor:"#E83737",critBorderColor:"#E83737",darkTextColor:"hsl(28.5714285714, 17.3553719008%, 86.2745098039%)",defaultLinkColor:"lightgrey",doneTaskBkgColor:"lightgrey",doneTaskBorderColor:"grey",edgeLabelBackground:"hsl(0, 0%, 34.4117647059%)",errorBkgColor:"#a44141",errorTextColor:"#ddd",fillType0:"#1f2020",fillType1:"hsl(180, 1.5873015873%, 28.3529411765%)",fillType2:"hsl(244, 1.5873015873%, 12.3529411765%)",fillType3:"hsl(244, 1.5873015873%, 28.3529411765%)",fillType4:"hsl(116, 1.5873015873%, 12.3529411765%)",fillType5:"hsl(116, 1.5873015873%, 28.3529411765%)",fillType6:"hsl(308, 1.5873015873%, 12.3529411765%)",fillType7:"hsl(308, 1.5873015873%, 28.3529411765%)",fontFamily:'"trebuchet ms", verdana, arial',fontSize:"16px",gridColor:"lightgrey",labelBackground:"#181818",labelBoxBkgColor:"#1f2020",labelBoxBorderColor:"#81B1DB",labelColor:"#ccc",labelTextColor:"lightgrey",lineColor:"lightgrey",loopTextColor:"lightgrey",mainBkg:"#1f2020",mainContrastColor:"lightgrey",nodeBkg:"#1f2020",nodeBorder:"#81B1DB",noteBkgColor:"#fff5ad",noteBorderColor:"rgba(255, 255, 255, 0.25)",noteTextColor:"#1f2020",primaryBorderColor:"hsl(180, 0%, 2.3529411765%)",primaryColor:"#1f2020",primaryTextColor:"#e0dfdf",secondBkg:"hsl(180, 1.5873015873%, 28.3529411765%)",secondaryBorderColor:"hsl(180, 0%, 18.3529411765%)",secondaryColor:"hsl(180, 1.5873015873%, 28.3529411765%)",secondaryTextColor:"rgb(183.8476190475, 181.5523809523, 181.5523809523)",sectionBkgColor:"hsl(52.9411764706, 28.813559322%, 58.431372549%)",sectionBkgColor2:"#EAE8D9",sequenceNumberColor:"black",signalColor:"lightgrey",signalTextColor:"lightgrey",taskBkgColor:"hsl(180, 1.5873015873%, 35.3529411765%)",taskBorderColor:"#ffffff",taskTextClickableColor:"#003163",taskTextColor:"hsl(28.5714285714, 17.3553719008%, 86.2745098039%)",taskTextDarkColor:"hsl(28.5714285714, 17.3553719008%, 86.2745098039%)",taskTextLightColor:"lightgrey",taskTextOutsideColor:"lightgrey",tertiaryBorderColor:"hsl(20, 0%, 2.3529411765%)",tertiaryColor:"hsl(20, 1.5873015873%, 12.3529411765%)",tertiaryTextColor:"rgb(222.9999999999, 223.6666666666, 223.9999999999)",textColor:"#ccc",titleColor:"#F9FFFE",todayLineColor:"#DB5757"}),mermaid.initialize(e),o.forEach((function(e){"true"!==e.getAttribute("data-processed")&&(mermaid.init(void 0,e),e.setAttribute("data-processed","true"))}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t,n){void 0===e&&(e=document),void 0===t&&(t=r.a.CDN);var o=e.querySelectorAll(".language-mindmap");o.length>0&&Object(i.a)(t+"/dist/js/echarts/echarts.min.js","vditorEchartsScript").then((function(){o.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")){var t=e.getAttribute("data-code");if(t)try{if("true"===e.getAttribute("data-processed"))return;echarts.init(e,"dark"===n?"dark":void 0).setOption({series:[{data:[JSON.parse(decodeURIComponent(t))],initialTreeDepth:-1,itemStyle:{borderWidth:0,color:"#4285f4"},label:{backgroundColor:"#f6f8fa",borderColor:"#d1d5da",borderRadius:5,borderWidth:.5,color:"#586069",lineHeight:20,offset:[-5,0],padding:[0,5],position:"insideRight"},lineStyle:{color:"#d1d5da",width:1},roam:!0,symbol:function(e,t){var n;return(null===(n=null==t?void 0:t.data)||void 0===n?void 0:n.children)?"circle":"path://"},type:"tree"}],tooltip:{trigger:"item",triggerOn:"mousemove"}}),e.setAttribute("data-processed","true")}catch(t){e.className="vditor-reset--error",e.innerHTML="mindmap render error: <br>"+t}}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){e&&e.querySelectorAll("a").forEach((function(e){var t=e.getAttribute("href");t&&(t.match(/^.+.(mp4|m4v|ogg|ogv|webm)$/)?function(e,t){e.insertAdjacentHTML("afterend",'<video controls="controls" src="'+t+'"></video>'),e.remove()}(e,t):t.match(/^.+.(mp3|wav|flac)$/)?function(e,t){e.insertAdjacentHTML("afterend",'<audio controls="controls" src="'+t+'"></audio>'),e.remove()}(e,t):function(e,t){var n=t.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?/),r=t.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),i=t.match(/\/\/v\.qq\.com\/x\/cover\/.*\/([^\/]+)\.html\??.*/),o=t.match(/(?:www\.|\/\/)coub\.com\/view\/(\w+)/),a=t.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/),c=t.match(/.+dailymotion.com\/(video|hub)\/(\w+)\?/),s=t.match(/(?:www\.|\/\/)bilibili\.com\/video\/(\w+)/),l=t.match(/(?:www\.|\/\/)ted\.com\/talks\/(\w+)/);n&&11===n[1].length?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//www.youtube.com/embed/'+n[1]+(n[2]?"?start="+n[2]:"")+'"></iframe>'),e.remove()):r&&r[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//player.youku.com/embed/'+r[1]+'"></iframe>'),e.remove()):i&&i[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="https://v.qq.com/txp/iframe/player.html?vid='+i[1]+'"></iframe>'),e.remove()):o&&o[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="//coub.com/embed/'+o[1]+'?muted=false&autostart=false&originalSize=true&startWithHD=true"></iframe>'),e.remove()):a&&a[0]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="https://www.facebook.com/plugins/video.php?href='+encodeURIComponent(a[0])+'"></iframe>'),e.remove()):c&&c[2]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="https://www.dailymotion.com/embed/video/'+c[2]+'"></iframe>'),e.remove()):s&&s[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="//player.bilibili.com/player.html?bvid='+s[1]+'"></iframe>'),e.remove()):l&&l[1]&&(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//embed.ted.com/talks/'+l[1]+'"></iframe>'),e.remove())}(e,t))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(8),o=function(e,t){void 0===t&&(t=r.a.CDN),r.a.CODE_THEME.includes(e)||(e="github");var n=document.getElementById("vditorHljsStyle"),o=t+"/dist/js/highlight.js/styles/"+e+".css";n?n.href!==o&&(n.remove(),Object(i.a)(o,"vditorHljsStyle")):Object(i.a)(o,"vditorHljsStyle")}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(6),i=n(7),o=function(e,t,n){var o="",a=[];if(Array.from(e.children).forEach((function(e,t){if(Object(r.a)(e)){if(n){var i=e.id.lastIndexOf("_");e.id=e.id.substring(0,-1===i?void 0:i)+"_"+t}a.push(e.id),o+=e.outerHTML.replace("<wbr>","")}})),""===o)return t.innerHTML="","";var c=document.createElement("div");if(n)"wysiwyg"!==n.currentMode||n.preview.element.contains(e)?"ir"!==n.currentMode||n.preview.element.contains(e)?c.innerHTML=n.lute.HTML2VditorDOM("<p>[ToC]</p>"+o):c.innerHTML=n.lute.SpinVditorIRDOM("<p>[ToC]</p>"+o):c.innerHTML=n.lute.SpinVditorDOM("<p>[ToC]</p>"+o);else{var s=Lute.New();s.SetToC(!0),c.innerHTML=s.HTML2VditorDOM("<p>[ToC]</p>"+o)}var l=c.firstElementChild.querySelectorAll("li > span[data-target-id]");return l.forEach((function(e,t){e.nextElementSibling&&"UL"===e.nextElementSibling.tagName?e.insertAdjacentHTML("afterbegin","<svg class='vditor-outline__action'><use xlink:href='#vditor-icon-down'></use></svg>"):e.insertAdjacentHTML("afterbegin","<svg class='vditor-outline__action'></svg>"),e.setAttribute("data-target-id",a[t])})),o=c.firstElementChild.innerHTML,0===l.length?(t.innerHTML="",o):(t.innerHTML=o,n&&Object(i.a)(t,{cdn:n.options.cdn,math:n.options.preview.math}),t.firstElementChild.addEventListener("click",(function(r){for(var i=r.target;i&&!i.isEqualNode(t);){if(i.classList.contains("vditor-outline__action")){i.classList.contains("vditor-outline__action--close")?(i.classList.remove("vditor-outline__action--close"),i.parentElement.nextElementSibling.setAttribute("style","display:block")):(i.classList.add("vditor-outline__action--close"),i.parentElement.nextElementSibling.setAttribute("style","display:none")),r.preventDefault(),r.stopPropagation();break}if(i.getAttribute("data-target-id")){r.preventDefault(),r.stopPropagation();var o=document.getElementById(i.getAttribute("data-target-id"));if(!o)return;if(n)if("auto"===n.options.height){var a=o.offsetTop+n.element.offsetTop;n.options.toolbarConfig.pin||(a+=n.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,a)}else n.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,n.element.offsetTop),n.preview.element.contains(e)?e.parentElement.scrollTop=o.offsetTop:e.scrollTop=o.offsetTop;else window.scrollTo(window.scrollX,o.offsetTop);break}i=i.parentElement}})),o)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n={},i=function(e){for(var t in e)e.hasOwnProperty(t)&&("[object Object]"===Object.prototype.toString.call(e[t])?n[t]=r(n[t],e[t]):n[t]=e[t])},o=0;o<e.length;o++)i(e[o]);return n}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){var t=Lute.New();return t.PutEmojis(e.emojis),t.SetEmojiSite(e.emojiSite),t.SetHeadingAnchor(e.headingAnchor),t.SetInlineMathAllowDigitAfterOpenMarker(e.inlineMathDigit),t.SetAutoSpace(e.autoSpace),t.SetToC(e.toc),t.SetFootnotes(e.footnotes),t.SetChinesePunct(e.chinesePunct),t.SetFixTermTypo(e.fixTermTypo),t.SetVditorCodeBlockPreview(e.codeBlockPreview),t.SetVditorMathBlockPreview(e.mathBlockPreview),t.SetSanitize(e.sanitize),t.SetChineseParagraphBeginningSpace(e.paragraphBeginningSpace),t.SetRenderListStyle(e.listStyle),t.SetLinkBase(e.linkBase),t.SetLinkPrefix(e.linkPrefix),t.SetMark(e.mark),e.lazyLoadImage&&t.SetImageLazyLoading(e.lazyLoadImage),t}},function(e,t,n){"use strict";n.r(t);var r=n(11),i=n(12),o=n(13),a=n(14),c=n(15),s=n(16),l=function(e){void 0===e&&(e=document);var t=function(e){var t=document.createElement("img");t.src=e.getAttribute("data-src"),t.addEventListener("load",(function(){e.getAttribute("style")||e.getAttribute("class")||e.getAttribute("width")||e.getAttribute("height")||t.naturalHeight>t.naturalWidth&&t.naturalWidth/t.naturalHeight<document.querySelector(".vditor-reset").clientWidth/(window.innerHeight-40)&&t.naturalHeight>window.innerHeight-40&&(e.style.height=window.innerHeight-40+"px"),e.src=t.src})),e.removeAttribute("data-src")};if(!("IntersectionObserver"in window))return e.querySelectorAll("img").forEach((function(e){e.getAttribute("data-src")&&t(e)})),!1;window.vditorImageIntersectionObserver?(window.vditorImageIntersectionObserver.disconnect(),e.querySelectorAll("img").forEach((function(e){window.vditorImageIntersectionObserver.observe(e)}))):(window.vditorImageIntersectionObserver=new IntersectionObserver((function(e){e.forEach((function(e){(void 0===e.isIntersecting?0!==e.intersectionRatio:e.isIntersecting)&&e.target.getAttribute("data-src")&&t(e.target)}))})),e.querySelectorAll("img").forEach((function(e){window.vditorImageIntersectionObserver.observe(e)})))},d=n(7),u=n(19),f=n(17),m=n(18),p=n(21),g=n(2),h=n(10),v=n(5),b=n(1),y=n(22),w=n(23),S=n(0),T=function(e,t){if(void 0===t&&(t="zh_CN"),"undefined"!=typeof speechSynthesis&&"undefined"!=typeof SpeechSynthesisUtterance){var n='<svg><use xlink:href="#vditor-icon-play"></use></svg>',r='<svg><use xlink:href="#vditor-icon-pause"></use></svg>',i=document.querySelector(".vditor-speech");if(!i){(i=document.createElement("div")).className="vditor-speech",document.body.insertAdjacentElement("beforeend",i);var o=function(){var e,n;return speechSynthesis.getVoices().forEach((function(r){r.lang===t.replace("_","-")&&(e=r),r.default&&(n=r)})),e||(e=n),e};void 0!==speechSynthesis.onvoiceschanged&&(speechSynthesis.onvoiceschanged=o);var a=o();i.onclick=function(){if("vditor-speech"===i.className){var e=new SpeechSynthesisUtterance(i.getAttribute("data-text"));e.voice=a,e.onend=function(){i.className="vditor-speech",speechSynthesis.cancel(),i.innerHTML=n},speechSynthesis.speak(e),i.className="vditor-speech vditor-speech--current",i.innerHTML=r}else speechSynthesis.speaking&&(speechSynthesis.paused?(speechSynthesis.resume(),i.innerHTML=r):(speechSynthesis.pause(),i.innerHTML=n));Object(S.h)(window.vditorSpeechRange)},document.body.addEventListener("click",(function(){""===getSelection().toString().trim()&&"block"===i.style.display&&(i.className="vditor-speech",speechSynthesis.cancel(),i.style.display="none")}))}e.addEventListener("mouseup",(function(e){var t=getSelection().toString().trim();if(speechSynthesis.cancel(),""!==getSelection().toString().trim()){window.vditorSpeechRange=getSelection().getRangeAt(0).cloneRange();var r=getSelection().getRangeAt(0).getBoundingClientRect();i.innerHTML=n,i.style.display="block",i.style.top=r.top+r.height+document.querySelector("html").scrollTop-20+"px",i.style.left=e.screenX+2+"px",i.setAttribute("data-text",t)}else"block"===i.style.display&&(i.className="vditor-speech",i.style.display="none")}))}},C=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function c(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},E=function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}},k=function(e){var t={anchor:0,cdn:g.a.CDN,customEmoji:{},emojiPath:(e&&e.emojiPath||g.a.CDN)+"/dist/images/emoji",hljs:g.a.HLJS_OPTIONS,icon:"ant",lang:"zh_CN",markdown:g.a.MARKDOWN_OPTIONS,math:g.a.MATH_OPTIONS,mode:"light",speech:{enable:!1},theme:g.a.THEME_OPTIONS};return Object(y.a)(t,e)},L=function(e,t){var n=k(t);return Object(v.a)(n.cdn+"/dist/js/lute/lute.min.js","vditorLuteScript").then((function(){var r=Object(w.a)({autoSpace:n.markdown.autoSpace,chinesePunct:n.markdown.chinesePunct,codeBlockPreview:n.markdown.codeBlockPreview,emojiSite:n.emojiPath,emojis:n.customEmoji,fixTermTypo:n.markdown.fixTermTypo,footnotes:n.markdown.footnotes,headingAnchor:0!==n.anchor,inlineMathDigit:n.math.inlineDigit,lazyLoadImage:n.lazyLoadImage,linkBase:n.markdown.linkBase,linkPrefix:n.markdown.linkPrefix,listStyle:n.markdown.listStyle,mark:n.markdown.mark,mathBlockPreview:n.markdown.mathBlockPreview,paragraphBeginningSpace:n.markdown.paragraphBeginningSpace,sanitize:n.markdown.sanitize,toc:n.markdown.toc});return(null==t?void 0:t.renderers)&&r.SetJSRenderers({renderers:{Md2HTML:t.renderers}}),r.Md2HTML(e)}))},x=function(e,t,n){return C(void 0,void 0,void 0,(function(){var p,g;return E(this,(function(y){switch(y.label){case 0:return p=k(n),[4,L(t,p)];case 1:return g=y.sent(),p.transform&&(g=p.transform(g)),e.innerHTML=g,e.classList.add("vditor-reset"),Object(h.a)(p.theme.current,p.theme.path),1===p.anchor&&e.classList.add("vditor-reset--anchor"),Object(o.a)(e,p.lang),Object(s.a)(p.hljs,e,p.cdn),Object(d.a)(e,{cdn:p.cdn,math:p.math}),Object(f.a)(e,p.cdn,p.mode),Object(a.a)(e,p.cdn),Object(c.a)(e,p.cdn),Object(i.a)(e,p.cdn,p.mode),Object(m.a)(e,p.cdn,p.mode),Object(r.a)(e,p.cdn),Object(u.a)(e),p.speech.enable&&T(e,p.lang),0!==p.anchor&&(w=p.anchor,document.querySelectorAll(".vditor-anchor").forEach((function(e){1===w&&e.classList.add("vditor-anchor--left"),e.onclick=function(){var t=e.getAttribute("href").substr(1),n=document.getElementById("vditorAnchor-"+t).offsetTop;document.querySelector("html").scrollTop=n}})),window.onhashchange=function(){var e=document.getElementById("vditorAnchor-"+decodeURIComponent(window.location.hash.substr(1)));e&&(document.querySelector("html").scrollTop=e.offsetTop)}),p.after&&p.after(),p.lazyLoadImage&&l(e),p.icon&&Object(v.a)(p.cdn+"/dist/js/icons/"+p.icon+".js","vditorIconScript"),e.addEventListener("click",(function(t){var n=Object(b.f)(t.target,"SPAN");if(n&&Object(b.e)(n,"vditor-toc")){var r=e.querySelector("#"+n.getAttribute("data-target-id"));r&&window.scrollTo(window.scrollX,r.offsetTop)}else;})),[2]}var w}))}))},j=n(20),O=function(){function e(){}return e.codeRender=o.a,e.graphvizRender=c.a,e.highlightRender=s.a,e.mathRender=d.a,e.mermaidRender=f.a,e.flowchartRender=a.a,e.chartRender=i.a,e.abcRender=r.a,e.mindmapRender=m.a,e.outlineRender=p.a,e.mediaRender=u.a,e.speechRender=T,e.lazyLoadImageRender=l,e.md2html=L,e.preview=x,e.setCodeTheme=j.a,e.setContentTheme=h.a,e}();t.default=O}]).default}));