# 配置文件的键名命名方式统一使用小驼峰。

# HTTP Server.
server:
  address:             ":8199"
  serverRoot:          "resource/public"
  dumpRouterMap:       true
  routeOverWrite:      true
  accessLogEnabled:    false
  accessLogPattern:    "access-{Ymd}.log"
  sessionPath:         "temp/sessions"    # Session文件存储目录
  openapiPath:         "/api.json"
  swaggerPath:         "/swagger"

# 数据库连接配置
database:
  logger:
    path:    "temp/logs/sql"
    level:   "all"
    stdout:  true
    ctxKeys: ["RequestId"]

  default:
    # link:   "mysql:root:12345678@tcp(127.0.0.1:3306)/focus"
    link:   "sqlite::@file(manifest/document/sqlite/focus.db)"
    debug:  true

# 内容设置
setting:
  title:       "Focus聚焦社区 - Beta!"
  keywords:    "Go,MVC,Cookie,Session,ORM,golang,cache,goframe,go frame,gf,go框架,web框架,高性能"
  description: "模块化、高性能、生产级的Go基础开发框架。实现了比较完善的基础设施建设，包括常用的核心开发组件， 如：缓存、日志、文件、时间、队列、数组、集合、字符串、定时器、命令行、文件锁、内存锁、对象池、连接池、资源管理、数据校验、数据编码、文件监控、 定时任务、数据库ORM、TCP/UDP组件、进程管理/通信、并发安全容器等等。 并提供了Web服务开发的系列核心组件，如：Router、Cookie、Session、Middleware、服务注册、配置管理、模板引擎等等， 支持热重启、热更新、多域名、多端口、多服务、HTTPS、Rewrite等特性。"
  adminIds:    [1] # 管理员ID

# 文件上传设置
upload:
  path: "temp/upload"

# Logger configurations.
logger:
  path:    "temp/logs/default"
  level:   "all"
  stdout:  true
  ctxKeys: ["RequestId"]

# 模板引擎配置
viewer:
  indexLayout:     "index/index.html"
  adminHomeLayout: "admin/home.html"
  adminLayout:     "admin/index.html"

