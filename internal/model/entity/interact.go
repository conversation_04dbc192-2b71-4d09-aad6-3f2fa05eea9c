// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Interact is the golang structure for table interact.
type Interact struct {
	Id         uint        `json:"id"         description:"自增ID"`
	Type       int         `json:"type"       description:"操作类型。0:赞，1:踩。"`
	UserId     uint        `json:"userId"     description:"操作用户"`
	TargetId   uint        `json:"targetId"   description:"对应内容ID，该内容可能是content, reply"`
	TargetType string      `json:"targetType" description:"内容模型: content, reply, 具体由程序定义"`
	Count      uint        `json:"count"      description:"操作数据值"`
	CreatedAt  *gtime.Time `json:"createdAt"  description:""`
	UpdatedAt  *gtime.Time `json:"updatedAt"  description:""`
}
