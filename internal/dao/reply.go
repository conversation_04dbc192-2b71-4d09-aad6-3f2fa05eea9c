// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"focus-single/internal/dao/internal"
)

// replyDao is the data access object for table gf_reply.
// You can define custom methods on it to extend its functionality as you wish.
type replyDao struct {
	*internal.ReplyDao
}

var (
	// Reply is globally public accessible object for table gf_reply operations.
	Reply = replyDao{
		internal.NewReplyDao(),
	}
)

// Fill with you ideas below.
