// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"
	"focus-single/internal/model"
	"focus-single/internal/model/entity"
)

type ICategory interface {
	GetTree(ctx context.Context, contentType string) ([]*model.CategoryTreeItem, error)
	GetSubIdList(ctx context.Context, id uint) ([]uint, error)
	GetList(ctx context.Context) (list []*entity.Category, err error)
	GetItem(ctx context.Context, id uint) (*entity.Category, error)
	GetMap(ctx context.Context) (map[uint]*entity.Category, error)
}

var localCategory ICategory

func Category() ICategory {
	if localCategory == nil {
		panic("implement not found for interface ICategory, forgot register?")
	}
	return localCategory
}

func RegisterCategory(i ICategory) {
	localCategory = i
}
