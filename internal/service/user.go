// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"
	"focus-single/internal/model"
	"focus-single/internal/model/entity"
)

type IUser interface {
	GetAvatarUploadPath() string
	GetAvatarUploadUrlPrefix() string
	Login(ctx context.Context, in model.UserLoginInput) error
	Logout(ctx context.Context) error
	EncryptPassword(passport, password string) string
	GetUserByPassportAndPassword(ctx context.Context, passport, password string) (user *entity.User, err error)
	CheckPassportUnique(ctx context.Context, passport string) error
	CheckNicknameUnique(ctx context.Context, nickname string) error
	Register(ctx context.Context, in model.UserRegisterInput) error
	UpdatePassword(ctx context.Context, in model.UserPasswordInput) error
	GetProfileById(ctx context.Context, userId uint) (out *model.UserGetProfileOutput, err error)
	GetProfile(ctx context.Context) (*model.UserGetProfileOutput, error)
	UpdateAvatar(ctx context.Context, in model.UserUpdateAvatarInput) error
	UpdateProfile(ctx context.Context, in model.UserUpdateProfileInput) error
	Disable(ctx context.Context, id uint) error
	GetList(ctx context.Context, in model.UserGetContentListInput) (out *model.UserGetListOutput, err error)
	GetMessageList(ctx context.Context, in model.UserGetMessageListInput) (out *model.UserGetMessageListOutput, err error)
	GetUserStats(ctx context.Context, userId uint) (map[string]int, error)
	IsCtxAdmin(ctx context.Context) bool
	IsAdmin(ctx context.Context, userId uint) bool
}

var localUser IUser

func User() IUser {
	if localUser == nil {
		panic("implement not found for interface IUser, forgot register?")
	}
	return localUser
}

func RegisterUser(i IUser) {
	localUser = i
}
