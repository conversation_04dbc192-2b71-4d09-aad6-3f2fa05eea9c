// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"
	"focus-single/internal/model"
)

type IContent interface {
	GetList(ctx context.Context, in model.ContentGetListInput) (out *model.ContentGetListOutput, err error)
	Search(ctx context.Context, in model.ContentSearchInput) (out *model.ContentSearchOutput, err error)
	GetDetail(ctx context.Context, id uint) (out *model.ContentGetDetailOutput, err error)
	Create(ctx context.Context, in model.ContentCreateInput) (out model.ContentCreateOutput, err error)
	Update(ctx context.Context, in model.ContentUpdateInput) error
	Delete(ctx context.Context, id uint) error
	AddViewCount(ctx context.Context, id uint, count int) error
	AddReplyCount(ctx context.Context, id uint, count int) error
	AdoptReply(ctx context.Context, id uint, replyID uint) error
	UnacceptedReply(ctx context.Context, id uint) error
}

var localContent IContent

func Content() IContent {
	if localContent == nil {
		panic("implement not found for interface IContent, forgot register?")
	}
	return localContent
}

func RegisterContent(i IContent) {
	localContent = i
}
