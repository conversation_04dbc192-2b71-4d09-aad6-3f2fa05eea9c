// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"
	"focus-single/internal/model"
)

type IMenu interface {
	SetTopMenus(ctx context.Context, menus []*model.MenuItem) error
	GetTopMenus(ctx context.Context) ([]*model.MenuItem, error)
	GetTopMenuByUrl(ctx context.Context, url string) (*model.MenuItem, error)
}

var localMenu IMenu

func Menu() IMenu {
	if localMenu == nil {
		panic("implement not found for interface IMenu, forgot register?")
	}
	return localMenu
}

func RegisterMenu(i IMenu) {
	localMenu = i
}
