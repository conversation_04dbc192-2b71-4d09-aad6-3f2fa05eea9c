// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"
	"focus-single/internal/model"
	"focus-single/internal/model/entity"
)

type ISession interface {
	SetUser(ctx context.Context, user *entity.User) error
	GetUser(ctx context.Context) *entity.User
	RemoveUser(ctx context.Context) error
	SetLoginReferer(ctx context.Context, referer string) error
	GetLoginReferer(ctx context.Context) string
	RemoveLoginReferer(ctx context.Context) error
	SetNotice(ctx context.Context, message *model.SessionNotice) error
	GetNotice(ctx context.Context) (*model.SessionNotice, error)
	RemoveNotice(ctx context.Context) error
}

var localSession ISession

func Session() ISession {
	if localSession == nil {
		panic("implement not found for interface ISession, forgot register?")
	}
	return localSession
}

func RegisterSession(i ISession) {
	localSession = i
}
