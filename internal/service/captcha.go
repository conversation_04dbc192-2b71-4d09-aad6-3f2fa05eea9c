// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package service

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

type ICaptcha interface {
	NewAndStore(ctx context.Context, name string) error
	VerifyAndClear(r *ghttp.Request, name string, value string) bool
}

var localCaptcha ICaptcha

func Captcha() ICaptcha {
	if localCaptcha == nil {
		panic("implement not found for interface ICaptcha, forgot register?")
	}
	return localCaptcha
}

func RegisterCaptcha(i ICaptcha) {
	localCaptcha = i
}
